import antfu from '@antfu/eslint-config';

export default antfu({
    // enable UnoCSS support
    // https://unocss.dev/integrations/vscode
    unocss: true,
    formatters: {
        css: true,
    },
    stylistic: {
        indent: 4,
        quotes: 'single',
        semi: true,
    },
    ignores: [
        'node_modules/',
        'dist/',
        'build/',
        '.vscode',
        '.husky',
        'build',
        'public',
        '*.yml',
        '*.md',
        '*.json',
        'stats.html',
        'pb.cjs',
        'pb-p.cjs',
        'pbmap2js.cjs',
        'reward-tool.js',
        '**/*.md',
    ],
    // 导入unplugin-auto-import的配置

    // 覆盖默认配置
    rules: {
        'no-console': 'warn',
        'no-debugger': 'warn',
        'antfu/top-level-function': 'off',
        // 允许promise reject不传入error对象
        'prefer-promise-reject-errors': 'off',
        // 允许正则表达式使用字符编码
        'no-control-regex': 'off',
        // 单行块无需隐藏大括号
        'antfu/curly': 'off',
        // 允许"_"开头命名的变量未使用
        'no-unused-vars': 'off',
        'unused-imports/no-unused-vars': 'warn',
        // 无需自动删除未使用的import
        'unused-imports/no-unused-imports': 'off',
        // 允许promise中使用async
        'no-async-promise-executor': 'off',
        // 不强制使用const
        'prefer-const': 'off',
        'style/indent-binary-ops': 'off',
        // 允许非必要的return
        'no-useless-return': 'off',
        // 允许注释不使用空格
        'style/spaced-comment': 'off',
        // 允许ts定义的变量未使用
        'ts/no-unused-expressions': 'off',
        /**
         * vue相关配置
         */
        // vue文件template、script、style之间排序规则
        'vue/block-order': 'off',
        // 标签属性独占一行
        'vue/max-attributes-per-line': [
            'warn',
            {
                singleline: 1,
                multiline: 1,
            },
        ],
        // 允许空对标签
        'vue/html-self-closing': 'off',
        // 自定义组件不校验驼峰、短横线命名
        'vue/component-name-in-template-casing': 'off',
        // 标签右尖括号“>”不换行
        'vue/html-closing-bracket-newline': 'off',
        // 单行标签内容不换行
        'vue/singleline-html-element-content-newline': 'off',
        // v-for key值不必填
        'vue/valid-v-for': 'off',
        'vue/require-v-for-key': 'off',
        // 允许vue template的变量未使用
        'vue/no-unused-vars': 'off',
    },
    languageOptions: {
        globals: {
            TTJSBridge: true,
            eruda: true,
            VConsole: true,
            PageSpy: true,
            DataHarborPlugin: true,
            RRWebPlugin: true,
            Component: true,
            ComponentPublicInstance: true,
            ComputedRef: true,
            DirectiveBinding: true,
            EffectScope: true,
            ExtractDefaultPropTypes: true,
            ExtractPropTypes: true,
            ExtractPublicPropTypes: true,
            InjectionKey: true,
            MARKET_ID: true,
            MARKET_ID_MAP: true,
            MaybeRef: true,
            MaybeRefOrGetter: true,
            PropType: true,
            Ref: true,
            VNode: true,
            WritableComputedRef: true,
            allowMultipleToast: true,
            asyncComputed: true,
            autoResetRef: true,
            avatarError: true,
            beans: true,
            closeDialog: true,
            closeNotify: true,
            closeToast: true,
            computed: true,
            computedAsync: true,
            computedEager: true,
            computedInject: true,
            computedWithControl: true,
            controlledComputed: true,
            controlledRef: true,
            createApp: true,
            createEventHook: true,
            createGlobalState: true,
            createInjectionState: true,
            createReactiveFn: true,
            createReusableTemplate: true,
            createSharedComposable: true,
            createTemplatePromise: true,
            createUnrefFn: true,
            customRef: true,
            debouncedRef: true,
            debouncedWatch: true,
            defineAsyncComponent: true,
            defineComponent: true,
            eagerComputed: true,
            effectScope: true,
            extendRef: true,
            formatTime: true,
            getActiveHead: true,
            getAvatar: true,
            getCurrentInstance: true,
            getCurrentScope: true,
            h: true,
            handleAvatar: true,
            ignorableWatch: true,
            initData: true,
            inject: true,
            injectHead: true,
            injectLocal: true,
            isDefined: true,
            isEnd: true,
            isProxy: true,
            isReactive: true,
            isReadonly: true,
            isRef: true,
            makeDestructurable: true,
            markRaw: true,
            myWebview: true,
            nextTick: true,
            num2percent: true,
            omitTxt: true,
            omitValue: true,
            onActivated: true,
            onBeforeMount: true,
            onBeforeRouteLeave: true,
            onBeforeRouteUpdate: true,
            onBeforeUnmount: true,
            onBeforeUpdate: true,
            onClickOutside: true,
            onDeactivated: true,
            onErrorCaptured: true,
            onKeyStroke: true,
            onLongPress: true,
            onMounted: true,
            onRenderTracked: true,
            onRenderTriggered: true,
            onScopeDispose: true,
            onServerPrefetch: true,
            onStartTyping: true,
            onUnmounted: true,
            onUpdated: true,
            onWatcherCleanup: true,
            parseUrlQuery: true,
            pausableWatch: true,
            provide: true,
            provideLocal: true,
            reactify: true,
            reactifyObject: true,
            reactive: true,
            reactiveComputed: true,
            reactiveOmit: true,
            reactivePick: true,
            readonly: true,
            ref: true,
            refAutoReset: true,
            refDebounced: true,
            refDefault: true,
            refThrottled: true,
            refWithControl: true,
            requireAssets: true,
            requireImg: true,
            requirePublic: true,
            resetDialogDefaultOptions: true,
            resetNotifyDefaultOptions: true,
            resetToastDefaultOptions: true,
            resolveComponent: true,
            resolveRef: true,
            resolveUnref: true,
            setDialogDefaultOptions: true,
            setNotifyDefaultOptions: true,
            setToastDefaultOptions: true,
            shallowReactive: true,
            shallowReadonly: true,
            shallowRef: true,
            showConfirmDialog: true,
            showDialog: true,
            showFailToast: true,
            showImagePreview: true,
            showLoading: true,
            showLoadingToast: true,
            showNotify: true,
            showToastToast: true,
            showToast: true,
            storeToRefs: true,
            supplementDouble: true,
            syncRef: true,
            syncRefs: true,
            templateRef: true,
            throttledRef: true,
            throttledWatch: true,
            to: true,
            toGuild: true,
            toPerson: true,
            toRaw: true,
            toReactive: true,
            toRef: true,
            toRefs: true,
            toRoom: true,
            toValue: true,
            triggerRef: true,
            tryOnBeforeMount: true,
            tryOnBeforeUnmount: true,
            tryOnMounted: true,
            tryOnScopeDispose: true,
            tryOnUnmounted: true,
            unref: true,
            unrefElement: true,
            until: true,
            useActiveElement: true,
            useAnimate: true,
            useArrayDifference: true,
            useArrayEvery: true,
            useArrayFilter: true,
            useArrayFind: true,
            useArrayFindIndex: true,
            useArrayFindLast: true,
            useArrayIncludes: true,
            useArrayJoin: true,
            useArrayMap: true,
            useArrayReduce: true,
            useArraySome: true,
            useArrayUnique: true,
            useAsyncQueue: true,
            useAsyncState: true,
            useAttrs: true,
            useBase64: true,
            useBattery: true,
            useBluetooth: true,
            useBreakpoints: true,
            useBroadcastChannel: true,
            useBrowserLocation: true,
            useCached: true,
            useClipboard: true,
            useClipboardItems: true,
            useCloned: true,
            useColorMode: true,
            useConfirmDialog: true,
            useCounter: true,
            useCssModule: true,
            useCssVar: true,
            useCssVars: true,
            useCurrentElement: true,
            useCycleList: true,
            useDark: true,
            useDateFormat: true,
            useDebounce: true,
            useDebounceFn: true,
            useDebouncedRefHistory: true,
            useDeviceMotion: true,
            useDeviceOrientation: true,
            useDevicePixelRatio: true,
            useDevicesList: true,
            useDisplayMedia: true,
            useDocumentVisibility: true,
            useDraggable: true,
            useDropZone: true,
            useElementBounding: true,
            useElementByPoint: true,
            useElementHover: true,
            useElementSize: true,
            useElementVisibility: true,
            useEventBus: true,
            useEventListener: true,
            useEventSource: true,
            useEyeDropper: true,
            useFavicon: true,
            useFetch: true,
            useFileDialog: true,
            useFileSystemAccess: true,
            useFocus: true,
            useFocusWithin: true,
            useFps: true,
            useFullscreen: true,
            useGamepad: true,
            useGeolocation: true,
            useHead: true,
            useHeadSafe: true,
            useId: true,
            useIdle: true,
            useImage: true,
            useInfiniteScroll: true,
            useIntersectionObserver: true,
            useInterval: true,
            useIntervalFn: true,
            useKeyModifier: true,
            useLastChanged: true,
            useLink: true,
            useLocalStorage: true,
            useMagicKeys: true,
            useManualRefHistory: true,
            useMediaControls: true,
            useMediaQuery: true,
            useMemoize: true,
            useMemory: true,
            useModel: true,
            useMounted: true,
            useMouse: true,
            useMouseInElement: true,
            useMousePressed: true,
            useMutationObserver: true,
            useNavigatorLanguage: true,
            useNetwork: true,
            useNow: true,
            useObjectUrl: true,
            useOffsetPagination: true,
            useOnline: true,
            usePageLeave: true,
            useParallax: true,
            useParentElement: true,
            usePerformanceObserver: true,
            usePermission: true,
            usePointer: true,
            usePointerLock: true,
            usePointerSwipe: true,
            usePreferredColorScheme: true,
            usePreferredContrast: true,
            usePreferredDark: true,
            usePreferredLanguages: true,
            usePreferredReducedMotion: true,
            usePrevious: true,
            useRafFn: true,
            useRefHistory: true,
            useResizeObserver: true,
            useRoute: true,
            useRouter: true,
            useScreenOrientation: true,
            useScreenSafeArea: true,
            useScriptTag: true,
            useScroll: true,
            useScrollLock: true,
            useSeoMeta: true,
            useServerHead: true,
            useServerHeadSafe: true,
            useServerSeoMeta: true,
            useSessionStorage: true,
            useShare: true,
            useSlots: true,
            useSorted: true,
            useSpeechRecognition: true,
            useSpeechSynthesis: true,
            useStepper: true,
            useStorage: true,
            useStorageAsync: true,
            useStyleTag: true,
            useSupported: true,
            useSwipe: true,
            useTemplateRef: true,
            useTemplateRefsList: true,
            useTextDirection: true,
            useTextSelection: true,
            useTextareaAutosize: true,
            useThrottle: true,
            useThrottleFn: true,
            useThrottledRefHistory: true,
            useTimeAgo: true,
            useTimeout: true,
            useTimeoutFn: true,
            useTimeoutPoll: true,
            useTimestamp: true,
            useTitle: true,
            useToNumber: true,
            useToString: true,
            useToggle: true,
            useTransition: true,
            useUrlSearchParams: true,
            useUserMedia: true,
            useVModel: true,
            useVModels: true,
            useVibrate: true,
            useVirtualList: true,
            useWakeLock: true,
            useWebNotification: true,
            useWebSocket: true,
            useWebWorker: true,
            useWebWorkerFn: true,
            useWindowFocus: true,
            useWindowScroll: true,
            useWindowSize: true,
            watch: true,
            watchArray: true,
            watchAtMost: true,
            watchDebounced: true,
            watchDeep: true,
            watchEffect: true,
            watchIgnorable: true,
            watchImmediate: true,
            watchOnce: true,
            watchPausable: true,
            watchPostEffect: true,
            watchSyncEffect: true,
            watchThrottled: true,
            watchTriggerable: true,
            watchWithFilter: true,
            whenever: true,
            calcPx: true,
            calcVw: true,
            serverTime: true,
            parse: true,
            stringify: true,
            truncate: true,
            useActivityStorage: true,
            safeOmitTxt: true,
            RULE_LINK: true,
            getAvatarBase64: true,
            getDirectAvatarUrl: true,
            getImageBase64: true,
            getPrice: true,
            getRewardInfo: true,
            jumpToLink: true,
            imageError: true,
        },
    },

});
