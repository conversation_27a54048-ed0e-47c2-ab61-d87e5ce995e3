<route lang="json">
    {
        "name": "home",
        "path": "/:pathMatch(.*)*",
        "meta": {
            "title": "电竞福利会场"
        }
    }
    </route>

<template>
    <page-container>
        <div
            ref="wrapper"
            class="home"
            @scroll="handleInfinite"
        >
            <div class="banner relative">
                <home-tab class="absolute top-[226px] z-[100]" />
                <img
                    class="absolute right-[0] top-[37px] z-100 h-[22px] w-[57.5px]"
                    :src="requireImg('<EMAIL>')"
                    @click="jumpToLink(RULE_LINK)">
            </div>
            <div
                v-if="initStore.serverTime"
                class="bg-content">
                <tab1 v-if="current === 1"></tab1>
                <tab2 v-if="current === 2"></tab2>
                <div
                    v-if="myWebview.isIOS()"
                    class="mt-[10px] w-full flex items-center justify-center text-[12px] text-[#E4C59D]"
                >
                    本活动与苹果公司无关
                </div>
            </div>
            <draw-record-modal></draw-record-modal>
            <task-modal></task-modal>
        </div>
    </page-container>
</template>

<script setup name="Home">
import { throttle } from 'lodash-es';
import useInitStore from '@/stores/modules/use-init-store';
import useDrawStore from '@/components/tab2/use-draw';
import { pageView, track } from '@/utils/jsbridge';

import useNav from '@/components/nav/use-nav';

const navStore = useNav();
const { current } = storeToRefs(navStore);
const drawStore = useDrawStore();

const wrapper = ref();

const router = useRouter();
const initStore = useInitStore();

onMounted(async () => {
    const { sourceform } = myWebview.params;
    pageView('activity_page', sourceform || 0);
    const toast = showLoading();
    await initStore.init({ flag: true });
    toast.close();
});

// 将事件处理函数抽离为独立的 composable
const useScrollHandler = () => {
    const handleInfinite = throttle(() => {
        const scrollTop = wrapper.value?.scrollTop;
        if (scrollTop + wrapper.value.clientHeight >= wrapper.value.scrollHeight - 30) {
            useEventBus('cp-rank').emit();
        }
    }, 1000);

    return { handleInfinite };
};
const { handleInfinite } = useScrollHandler();
</script>

    <style lang="less" scoped>
    .home {
    overflow: auto;
    background: #2d0607;
    width: 100vw;
    min-height: 100vh;
    height: 100vh;

    .banner {
        width: 375px;
        height: 275px;
        background-image: url('@/assets/img/<EMAIL>');
        background-size: 100% 100%;
    }

    .bg-content {
        width: 100%;
        // background: #5adbf5;
        background-size: 100% auto;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}
</style>
