<route lang="json">
{
    "name": "GodTask",
    "path": "/god-task",
    "meta": {
        "title": "周年限时任务"
    }
}
</route>

<template>
    <page-container>
        <div class="god-task">
            <!-- 页面头部 -->
            <div class="task-header">
                <div class="title">周年限时任务</div>
                <div class="subtitle">每天完单人数达标可获得对应奖励</div>
            </div>

            <!-- 今日进度概览 -->
            <div class="progress-overview">
                <div class="progress-card">
                    <div class="progress-title">今日完单进度</div>
                    <div class="progress-content">
                        <div class="progress-number">
                            <span class="current">{{ todayProgress.current }}</span>
                            <span class="separator">/</span>
                            <span class="total">{{ todayProgress.total }}</span>
                        </div>
                        <div class="progress-label">已完成订单数</div>
                    </div>
                    <ProgressBar
                        :percentage="progressPercentage"
                        :show-text="false"
                        height="8px"
                        color="#ff6b6b" />
                </div>
            </div>

            <!-- 任务列表 -->
            <div class="task-list">
                <div class="task-section-title">每日任务</div>
                <div
                    v-for="task in taskList"
                    :key="task.id"
                    class="task-item"
                    :class="{ completed: task.completed, available: task.available }"
                    @click="onTaskClick(task)">
                    <div class="task-icon">
                        <img
                            v-if="task.icon"
                            :src="task.icon"
                            alt="任务图标" />
                        <div
                            v-else
                            class="default-icon">
                            📋
                        </div>
                    </div>

                    <div class="task-content">
                        <div class="task-title">{{ task.title }}</div>
                        <div class="task-desc">{{ task.description }}</div>
                        <div class="task-progress">进度: {{ task.current }}/{{ task.target }}</div>
                    </div>

                    <div class="task-reward">
                        <div class="reward-icon">🎁</div>
                        <div class="reward-text">{{ task.reward }}</div>
                    </div>

                    <div class="task-status">
                        <div
                            v-if="task.completed"
                            class="status-completed">
                            已完成
                        </div>
                        <div
                            v-else-if="task.available"
                            class="status-available">
                            可领取
                        </div>
                        <div
                            v-else
                            class="status-progress">
                            进行中
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务详情弹窗 -->
            <ModalContainer v-model:show="showTaskModal">
                <div class="task-modal">
                    <div class="modal-header">
                        <div class="modal-title">{{ selectedTask?.title }}</div>
                        <div
                            class="modal-close"
                            @click="showTaskModal = false">
                            ×
                        </div>
                    </div>

                    <div class="modal-content">
                        <div class="task-detail">
                            <div class="detail-item">
                                <span class="label">任务要求:</span>
                                <span class="value">{{ selectedTask?.description }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">当前进度:</span>
                                <span class="value">
                                    {{ selectedTask?.current }}/{{ selectedTask?.target }}
                                </span>
                            </div>
                            <div class="detail-item">
                                <span class="label">任务奖励:</span>
                                <span class="value reward">{{ selectedTask?.reward }}</span>
                            </div>
                        </div>

                        <ProgressBar
                            v-if="selectedTask"
                            :percentage="(selectedTask.current / selectedTask.target) * 100"
                            :show-text="true"
                            height="12px"
                            color="#ff6b6b" />

                        <div class="modal-actions">
                            <div
                                v-if="selectedTask?.available"
                                class="action-btn primary"
                                @click="claimReward(selectedTask)">
                                领取奖励
                            </div>
                            <div
                                v-else-if="!selectedTask?.completed"
                                class="action-btn secondary"
                                @click="goToComplete(selectedTask)">
                                去完成
                            </div>
                            <div
                                v-else
                                class="action-btn disabled">
                                已完成
                            </div>
                        </div>
                    </div>
                </div>
            </ModalContainer>
        </div>
    </page-container>
</template>

<script setup name="GodTask">
import useGodTaskStore from '@/stores/modules/use-god-task-store';

const godTaskStore = useGodTaskStore();
const { taskList, todayProgress, isLoading } = storeToRefs(godTaskStore);

const showTaskModal = ref(false);
const selectedTask = ref(null);

// 计算今日进度百分比
const progressPercentage = computed(() => {
    if (!todayProgress.value.total)
        return 0;
    return Math.min((todayProgress.value.current / todayProgress.value.total) * 100, 100);
});

// 任务点击
function onTaskClick(task) {
    selectedTask.value = task;
    showTaskModal.value = true;
}

// 领取奖励
async function claimReward(task) {
    try {
        const toast = showLoading('领取中...');
        await godTaskStore.claimReward(task.id);
        toast.close();
        showToast('奖励领取成功！');
        showTaskModal.value = false;
    }
    catch (error) {
        showToast(error.message || '领取失败，请重试');
    }
}

// 去完成任务
function goToComplete(task) {
    showTaskModal.value = false;
    // 根据任务类型跳转到对应页面
    // 这里可以根据具体需求实现跳转逻辑
    showToast('请前往完成任务');
}

onMounted(async () => {
    await godTaskStore.init();
});

onActivated(() => {
    // 页面激活时刷新任务数据
    godTaskStore.refreshTasks();
});
</script>

<style lang="less" scoped>
.god-task {
    min-height: 100vh;
    background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
    padding: 20px;

    .task-header {
        text-align: center;
        margin-bottom: 24px;

        .title {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: #999;
        }
    }

    .progress-overview {
        margin-bottom: 24px;

        .progress-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            backdrop-filter: blur(10px);

            .progress-title {
                font-size: 16px;
                color: #fff;
                margin-bottom: 12px;
                text-align: center;
            }

            .progress-content {
                text-align: center;
                margin-bottom: 16px;

                .progress-number {
                    font-size: 32px;
                    font-weight: bold;
                    color: #ff6b6b;
                    margin-bottom: 4px;

                    .separator {
                        color: #666;
                        margin: 0 4px;
                    }

                    .total {
                        color: #999;
                    }
                }

                .progress-label {
                    font-size: 12px;
                    color: #ccc;
                }
            }
        }
    }

    .task-list {
        .task-section-title {
            font-size: 18px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 16px;
        }

        .task-item {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            &.completed {
                opacity: 0.6;
            }

            &.available {
                border: 2px solid #ff6b6b;
                background: rgba(255, 107, 107, 0.1);
            }

            .task-icon {
                width: 48px;
                height: 48px;
                margin-right: 12px;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 8px;
                }

                .default-icon {
                    font-size: 24px;
                }
            }

            .task-content {
                flex: 1;

                .task-title {
                    font-size: 16px;
                    font-weight: bold;
                    color: #fff;
                    margin-bottom: 4px;
                }

                .task-desc {
                    font-size: 12px;
                    color: #999;
                    margin-bottom: 4px;
                }

                .task-progress {
                    font-size: 12px;
                    color: #ccc;
                }
            }

            .task-reward {
                text-align: center;
                margin-right: 12px;

                .reward-icon {
                    font-size: 20px;
                    margin-bottom: 4px;
                }

                .reward-text {
                    font-size: 12px;
                    color: #ffa500;
                    font-weight: bold;
                }
            }

            .task-status {
                .status-completed {
                    background: #4caf50;
                    color: #fff;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                }

                .status-available {
                    background: #ff6b6b;
                    color: #fff;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                }

                .status-progress {
                    background: #666;
                    color: #fff;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                }
            }
        }
    }
}

.task-modal {
    background: #fff;
    border-radius: 16px;
    max-width: 350px;
    width: 90%;
    overflow: hidden;

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #eee;

        .modal-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .modal-close {
            font-size: 24px;
            color: #999;
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .modal-content {
        padding: 20px;

        .task-detail {
            margin-bottom: 16px;

            .detail-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;

                .label {
                    color: #666;
                    font-size: 14px;
                }

                .value {
                    color: #333;
                    font-size: 14px;
                    font-weight: bold;

                    &.reward {
                        color: #ff6b6b;
                    }
                }
            }
        }

        .modal-actions {
            margin-top: 20px;

            .action-btn {
                width: 100%;
                text-align: center;
                padding: 12px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s;

                &.primary {
                    background: #ff6b6b;
                    color: #fff;

                    &:hover {
                        background: #ff5252;
                    }
                }

                &.secondary {
                    background: #f0f0f0;
                    color: #333;

                    &:hover {
                        background: #e0e0e0;
                    }
                }

                &.disabled {
                    background: #ccc;
                    color: #999;
                    cursor: not-allowed;
                }
            }
        }
    }
}
</style>
