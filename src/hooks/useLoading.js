export default function useLoading(loading) {
    const $loading = ref(null);

    watch(
        () => loading.value,
        (val) => {
            if (val) {
                $loading.value = showLoading();
            }
            else {
                $loading.value.close();
                $loading.value = null;
            }
        },
    );

    onBeforeUnmount(() => {
        if ($loading.value) {
            $loading.value.close();
            $loading.value = null;
        }
    });

    return {
        $loading,
    };
}
