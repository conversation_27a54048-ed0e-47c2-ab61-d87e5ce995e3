# 需求文档-25年周年庆电竞限时福利

## 一、活动概述

**活动时间：** 7月15日12：00-7月29日23：59

**参与对象：** 大神、全体用户

**活动目标：** 提升完单量&用户进房转化

## 二、活动原型

原型链接：https://kwmzef.axshare.com

![电竞会场](电竞会场.png)

## 三、玩法详情

### 【大神】周年大神榜

根据活动期间大神获取的大神值进行排名，根据大神6月接单量分为3个赛区比拼。

#### 大神值获取方式：
- 每新增一个下单并完单的用户 = 10大神值
- 活动期间下单的用户首次在pgc房间为自己下过单的大神打赏 = 100大神值

#### 榜单奖励

| 排名 | 奖励内容 | 备注 |
|------|----------|------|
| 第1名 | [表格内容待补充] | 根据赛区分别设置 |
| 第2名 | [表格内容待补充] | 根据赛区分别设置 |
| 第3名 | [表格内容待补充] | 根据赛区分别设置 |
| ... | ... | ... |

*注：具体奖励内容需要从飞书文档中的表格获取*

#### 页面交互
常规榜单交互即可，无需据上下名，榜单仅展示前100名，仅取个人视角

### 【大神】周年限时任务

活动期间，大神每天完单人数达标可获得对应奖励

#### 任务奖励表

| 完单人数 | 奖励内容 | 备注 |
|----------|----------|------|
| [待补充] | [待补充] | 每日任务 |
| [待补充] | [待补充] | 每日任务 |

*注：具体任务内容需要从飞书文档中的表格获取*

#### 页面交互
点击拉起任务弹窗，展示今日完成数值，若对应任务已完成则展示已完成下标，取个人视角

![任务界面](image.png)

### 【用户】邀好友下单福利

活动期间，用户完成以下任务可获得抽奖机会：

#### 任务列表

| 任务类型 | 任务要求 | 获得奖励 | 备注 |
|----------|----------|----------|------|
| 下单任务 | [待补充] | 抽奖机会 | 跳转至电竞首页 |
| 送礼任务 | [待补充] | 抽奖机会 | 跳转至指定房间 |
| 邀请任务 | [待补充] | 抽奖机会 | 分享邀请链接 |

*注：具体任务要求需要从飞书文档中的表格获取*

#### 抽奖奖励

| 奖励等级 | 奖励内容 | 概率 | 备注 |
|----------|----------|------|------|
| 一等奖 | [待补充] | [待补充] | 稀有奖励 |
| 二等奖 | [待补充] | [待补充] | 普通奖励 |
| 三等奖 | [待补充] | [待补充] | 基础奖励 |

*注：具体奖励内容需要从飞书文档中的表格获取*

#### 交互逻辑

![交互界面1](image.png)
![交互界面2](image.png)
![交互界面3](image.png)

#### 去完成跳转逻辑：
- **下单任务：** 跳转至电竞首页（后续提供链接）
- **送礼任务：** 跳转至指定房间
- **邀请任务：** 拉起邀请渠道浮层并分享至对应渠道

![邀请界面](image.png)

#### 分享渠道：
- **微信/QQ：** 端外链接分享
- **TT：** 端内链接分享

#### 邀请记录功能：
点击我的邀请记录拉起半屏，展示当前用户邀请的其他用户信息，仅展示成功接受邀请的用户

**状态显示：**
- 成功接受邀请的，展示已受邀状态
- 成功接受邀请且完成下单的，展示已下单状态

#### 邀请分享页面逻辑：
邀请分享链接为单独活动页，展示邀请者头像昵称ttid和宝箱奖励，两个按钮的点击逻辑根据操作场景是否在端外做区分

**在端内操作：**
- 点击再考虑下则跳转到原活动页面
- 点击接受邀请做以下判断：
  - 当前账号若已绑定其他用户，则toast：你已和【用户昵称】【ttid：xxxxx】绑定邀请关系了噢~
  - 当前账号若已在活动期间下过单，则toast：你已经在活动期间内下过单，不符合受邀条件噢~
  - 当前设备若已在活动期间下过单，则toast：当前设备已在活动期间内下过单，不符合受邀条件噢~
  - 若以上均验证通过，则双方关系绑定生效并toast：你已成功接受邀请~，并自动跳转至原活动页面

**在端外操作：**
- 点击拉起TT语音，成功拉起后在app里跳转至活动邀请页面，后续流程和在端内的一致
- 若当前设备未下载TT语音，则跳转至应用商店（与赏金赛逻辑一致即可）

## 四、项目时间节点

- **页面交付：** 6.20
- **页面提测：** 7.3
- **活动上线：** 7.15

## 五、技术实现要点

### 核心功能模块
1. **大神榜单系统** - 排行榜展示、个人视角、实时更新
2. **任务系统** - 每日任务、进度追踪、奖励发放
3. **邀请系统** - 分享链接、关系绑定、状态追踪
4. **抽奖系统** - 概率控制、奖励发放、记录查询

### 页面结构
- 主页面：活动入口、功能导航
- 大神榜单页：排行展示、个人信息
- 任务页面：任务列表、进度显示
- 邀请页面：分享功能、记录查询
- 抽奖页面：抽奖动画、结果展示

### 数据交互
- 用户信息获取与验证
- 排行榜数据实时更新
- 任务进度同步
- 邀请关系绑定
- 奖励发放记录
