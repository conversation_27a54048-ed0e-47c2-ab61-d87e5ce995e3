// live-anniversary-esports-venue/src/stores/modules/use-god-task-store.js
import { defineStore } from 'pinia';
import { getTaskList } from '@/api';

/**
 * 大神任务Store
 * 管理大神每日任务数据、进度追踪、奖励领取等
 */
export default defineStore('godTask', () => {
    // 状态定义
    const taskList = ref([]); // 任务列表
    const todayProgress = ref({ current: 0, total: 0 }); // 今日完单进度
    const isLoading = ref(false); // 加载状态
    const lastRefreshTime = ref(0); // 上次刷新时间

    // 任务模板配置
    const taskTemplates = ref([
        {
            id: 1,
            title: '完成1单',
            description: '今日完成1个订单',
            target: 1,
            reward: '积分 +50',
            icon: '/images/task-icon-1.png',
        },
        {
            id: 2,
            title: '完成3单',
            description: '今日完成3个订单',
            target: 3,
            reward: '积分 +150',
            icon: '/images/task-icon-3.png',
        },
        {
            id: 3,
            title: '完成5单',
            description: '今日完成5个订单',
            target: 5,
            reward: '积分 +300',
            icon: '/images/task-icon-5.png',
        },
        {
            id: 4,
            title: '完成10单',
            description: '今日完成10个订单',
            target: 10,
            reward: '积分 +800',
            icon: '/images/task-icon-10.png',
        },
    ]);

    // 获取任务列表
    async function fetchTasks() {
        if (isLoading.value)
            return;

        try {
            isLoading.value = true;

            const [data, error] = await to(getTaskList());

            if (error) {
                throw new Error(error.message || '获取任务列表失败');
            }

            if (data) {
                const { tasks = [], todayComplete = 0 } = data;

                // 更新今日进度
                todayProgress.value.current = todayComplete;
                todayProgress.value.total = Math.max(...taskTemplates.value.map(t => t.target));

                // 处理任务数据
                taskList.value = taskTemplates.value.map((template) => {
                    const taskData = tasks.find(t => t.taskId === template.id) || {};

                    return {
                        ...template,
                        current: todayComplete,
                        completed: taskData.completed || false,
                        available: todayComplete >= template.target && !taskData.completed,
                        claimedAt: taskData.claimedAt || null,
                    };
                });
            }
        }
        catch (error) {
            console.error('获取任务列表失败:', error);
            showToast(error.message || '获取任务列表失败');
        }
        finally {
            isLoading.value = false;
        }
    }

    // 刷新任务数据
    async function refreshTasks() {
        // 避免频繁刷新
        const now = Date.now();
        if (now - lastRefreshTime.value < 5000)
            return;

        lastRefreshTime.value = now;
        await Promise.all([
            fetchTasks(),
        ]);
    }

    // 初始化
    async function init() {
        await fetchTasks();
    }

    // 重置状态
    function reset() {
        taskList.value = [];
        todayProgress.value = { current: 0, total: 0 };
        isLoading.value = false;
        lastRefreshTime.value = 0;
    }

    // 获取可领取任务数量
    const availableTaskCount = computed(() => {
        return taskList.value.filter(task => task.available).length;
    });

    // 获取已完成任务数量
    const completedTaskCount = computed(() => {
        return taskList.value.filter(task => task.completed).length;
    });

    // 获取今日进度百分比
    const todayProgressPercentage = computed(() => {
        if (!todayProgress.value.total)
            return 0;
        return Math.min((todayProgress.value.current / todayProgress.value.total) * 100, 100);
    });

    // 获取下一个可完成的任务
    const nextAvailableTask = computed(() => {
        const current = todayProgress.value.current;
        return taskList.value.find(task => !task.completed && task.target > current);
    });

    return {
        // 状态
        taskList,
        todayProgress,
        isLoading,
        taskTemplates,

        // 计算属性
        availableTaskCount,
        completedTaskCount,
        todayProgressPercentage,
        nextAvailableTask,

        // 方法
        fetchTasks,
        refreshTasks,
        init,
        reset,
    };
});
