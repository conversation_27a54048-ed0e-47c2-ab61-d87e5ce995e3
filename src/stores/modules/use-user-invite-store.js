// live-anniversary-esports-venue/src/stores/modules/use-user-invite-store.js
import { defineStore } from 'pinia';
import {
    claimInviteTaskReward,
    generateInviteLink,
    getInviteRecords,
    getInviteStats,
    getUserInviteTasks,
} from '@/api';

/**
 * 用户邀请Store
 * 管理邀请任务、抽奖机会、邀请记录等
 */
export default defineStore('userInvite', () => {
    // 状态定义
    const taskList = ref([]); // 任务列表
    const lotteryCount = ref(0); // 抽奖次数
    const inviteStats = ref({ invited: 0, accepted: 0, ordered: 0 }); // 邀请统计
    const inviteRecords = ref([]); // 邀请记录
    const inviteLink = ref(''); // 邀请链接
    const isLoading = ref(false); // 加载状态

    // 任务模板配置
    const taskTemplates = ref([
        {
            id: 1,
            type: 'order',
            title: '下单任务',
            description: '完成1次下单',
            target: 1,
            reward: '1次抽奖机会',
            icon: '/images/task-order.png',
            jumpUrl: '', // 后续提供
        },
        {
            id: 2,
            type: 'gift',
            title: '送礼任务',
            description: '在指定房间送礼',
            target: 1,
            reward: '1次抽奖机会',
            icon: '/images/task-gift.png',
            jumpUrl: '', // 跳转至指定房间
        },
        {
            id: 3,
            type: 'invite',
            title: '邀请任务',
            description: '邀请1位好友下单',
            target: 1,
            reward: '2次抽奖机会',
            icon: '/images/task-invite.png',
        },
    ]);

    // 获取任务列表
    async function fetchTasks() {
        if (isLoading.value)
            return;

        try {
            isLoading.value = true;

            const [data, error] = await to(getUserInviteTasks());

            if (error) {
                throw new Error(error.message || '获取任务列表失败');
            }

            if (data) {
                const { tasks = [], lotteryChance = 0 } = data;

                // 更新抽奖次数
                lotteryCount.value = lotteryChance;

                // 处理任务数据
                taskList.value = taskTemplates.value.map((template) => {
                    const taskData = tasks.find(t => t.taskId === template.id) || {};

                    return {
                        ...template,
                        current: taskData.current || 0,
                        completed: taskData.completed || false,
                        canClaim: taskData.canClaim || false,
                        claimedAt: taskData.claimedAt || null,
                    };
                });
            }
        }
        catch (error) {
            console.error('获取任务列表失败:', error);
            showToast(error.message || '获取任务列表失败');
        }
        finally {
            isLoading.value = false;
        }
    }

    // 获取邀请统计
    async function fetchInviteStats() {
        try {
            const [data, error] = await to(getInviteStats());

            if (error) {
                throw new Error(error.message || '获取邀请统计失败');
            }

            if (data) {
                inviteStats.value = {
                    invited: data.invitedCount || 0,
                    accepted: data.acceptedCount || 0,
                    ordered: data.orderedCount || 0,
                };
            }
        }
        catch (error) {
            console.error('获取邀请统计失败:', error);
        }
    }

    // 获取邀请记录
    async function fetchInviteRecords() {
        try {
            const [data, error] = await to(getInviteRecords());

            if (error) {
                throw new Error(error.message || '获取邀请记录失败');
            }

            if (data) {
                inviteRecords.value = data.records || [];
            }
        }
        catch (error) {
            console.error('获取邀请记录失败:', error);
        }
    }

    // 生成邀请链接
    async function fetchInviteLink() {
        try {
            const [data, error] = await to(generateInviteLink());

            if (error) {
                throw new Error(error.message || '生成邀请链接失败');
            }

            if (data) {
                inviteLink.value = data.inviteLink || '';
            }
        }
        catch (error) {
            console.error('生成邀请链接失败:', error);
        }
    }

    // 领取任务奖励
    async function claimTaskReward(taskId) {
        const task = taskList.value.find(t => t.id === taskId);
        if (!task || !task.canClaim) {
            throw new Error('任务不可领取');
        }

        try {
            const [data, error] = await to(claimInviteTaskReward({ taskId }));

            if (error) {
                throw new Error(error.message || '领取奖励失败');
            }

            if (data) {
                // 更新任务状态
                const taskIndex = taskList.value.findIndex(t => t.id === taskId);
                if (taskIndex !== -1) {
                    taskList.value[taskIndex] = {
                        ...taskList.value[taskIndex],
                        completed: true,
                        canClaim: false,
                        claimedAt: new Date().toISOString(),
                    };
                }

                // 更新抽奖次数
                lotteryCount.value += data.lotteryChance || 0;

                return data;
            }
        }
        catch (error) {
            console.error('领取奖励失败:', error);
            throw error;
        }
    }

    // 刷新数据
    async function refreshData() {
        await Promise.all([
            fetchTasks(),
            fetchInviteStats(),
            fetchInviteRecords(),
        ]);
    }

    // 初始化
    async function init() {
        await Promise.all([
            fetchTasks(),
            fetchInviteStats(),
            fetchInviteRecords(),
            fetchInviteLink(),
        ]);
    }

    // 重置状态
    function reset() {
        taskList.value = [];
        lotteryCount.value = 0;
        inviteStats.value = { invited: 0, accepted: 0, ordered: 0 };
        inviteRecords.value = [];
        inviteLink.value = '';
        isLoading.value = false;
    }

    // 获取邀请链接
    function getInviteLink() {
        return inviteLink.value;
    }

    // 获取可领取任务数量
    const availableTaskCount = computed(() => {
        return taskList.value.filter(task => task.canClaim).length;
    });

    // 获取已完成任务数量
    const completedTaskCount = computed(() => {
        return taskList.value.filter(task => task.completed).length;
    });

    // 获取任务完成进度
    const taskProgress = computed(() => {
        const total = taskList.value.length;
        const completed = completedTaskCount.value;
        return total > 0 ? (completed / total) * 100 : 0;
    });

    // 检查是否有新的邀请记录
    const hasNewInvite = computed(() => {
        return inviteRecords.value.some((record) => {
            const recordTime = new Date(record.inviteTime).getTime();
            const now = Date.now();
            return now - recordTime < 24 * 60 * 60 * 1000; // 24小时内
        });
    });

    return {
        // 状态
        taskList,
        lotteryCount,
        inviteStats,
        inviteRecords,
        inviteLink,
        isLoading,
        taskTemplates,

        // 计算属性
        availableTaskCount,
        completedTaskCount,
        taskProgress,
        hasNewInvite,

        // 方法
        fetchTasks,
        fetchInviteStats,
        fetchInviteRecords,
        fetchInviteLink,
        claimTaskReward,
        refreshData,
        init,
        reset,
        getInviteLink,
    };
});
