// 执行pb.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import request from '../utils/request';
import getMockData from './mockData';

export const fetchApi = ({ proPrefix = '/activity.Activity/', api, data = {}, config = {} }) => {
    const { mock } = myWebview.params;
    if (mock)
        return to(getMockData(api, data));

    const url = `${proPrefix}/${api}`.replace('//', '/');
    return to(request.post(url, data, config));
};

/**
 * ********************************
 * *********  活动接口 *************
 * ********************************
 */
const REQUEST_API_MAP = {
    init: 'init',
    // 榜单相关
    getRank: 'getRank',
    // 抽奖相关
    draw: 'draw',
    getDrawBroadcast: 'getDrawBroadcast',
    myPrizeRecord: 'myPrizeRecord',
    getDrawInfo: 'getDrawInfo',
    // 任务相关
    getTaskList: 'getTaskList',
    // 邀请相关
    myInviteRecord: 'myInviteRecord',
    jumpRandomRoom: 'jumpRandomRoom',
    invite: 'invite',
};

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').InitRes},any]>} */
export const init = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.init, data, config });

// 榜单相关接口
/** @type {function(import('./api.d.ts').GetRankReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetRankRes},any]>} */
export const getRank = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getRank, data, config });

// 抽奖相关接口
/** @type {function(import('./api.d.ts').DrawReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').DrawRes},any]>} */
export const draw = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.draw, data, config });

export const getDrawBroadcast = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getDrawBroadcast, data, config });

/** @type {function(import('./api.d.ts').MyPrizeRecordReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').MyPrizeRecordRes},any]>} */
export const myPrizeRecord = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.myPrizeRecord, data, config });

export const getDrawInfo = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getDrawInfo, data, config });

// 任务相关接口
export const getTaskList = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getTaskList, data, config });

// 邀请相关接口
/** @type {function(import('./api.d.ts').MyInviteRecordReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').MyInviteRecordRes},any]>} */
export const myInviteRecord = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.myInviteRecord, data, config });

export const jumpRandomRoom = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.jumpRandomRoom, data, config });

/** @type {function(import('./api.d.ts').InviteReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').EmptyRes},any]>} */
export const invite = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.invite, data, config });

export default {
    init,
    // 榜单相关
    getRank,
    // 抽奖相关
    draw,
    getDrawBroadcast,
    myPrizeRecord,
    getDrawInfo,
    // 任务相关
    getTaskList,
    // 邀请相关
    myInviteRecord,
    jumpRandomRoom,
    invite,
};
