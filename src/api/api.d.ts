interface InitReq {
    uid: number;
}

interface InitResp {
    /**服务器时间 */
    serverTime: number;
    /**活动开始时间 */
    startTime: number;
    /**活动结束时间 */
    endTime: number;
}

// 排行榜相关接口定义
interface GetRankReq {
    page?: number;
    size?: number;
    uid: number;
    date?: string;
    isDs?: number;
}

interface UserInfo {
    uid: number;
    username: string;
    alias: string;
    nickname: string;
    sex: number;
    guildInfo?: {
        name: string;
        guildId: number;
        displayId: number;
    };
    role: number;
}

interface ChannelInfo {
    channelId: number;
    status: number;
    isPartChannel?: boolean;
}

interface RankItem {
    rank: string;
    value: number;
    userInfo: UserInfo;
    channelInfo?: ChannelInfo;
    money: number;
}

interface MyRankInfo {
    userInfo: UserInfo;
    rank: string;
    value: number;
    prevDescribe: string;
    nextDescribe: string;
    money: number;
}

interface GetRankRes {
    total: number;
    list: RankItem[];
    self?: MyRankInfo;
}

export type none = {};
