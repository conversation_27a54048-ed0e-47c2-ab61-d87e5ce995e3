<!--
  common-components/rank-item/rank-item.vue
  排行榜项目组件 - 提供统一的排名项展示，支持头像、昵称、数值、排名等
-->
<template>
    <div
        class="rank-item"
        :class="[
            containerClass,
            {
                'is-me': isMe,
                'is-top3': isTop3,
                'is-empty': isEmpty,
                'is-clickable': clickable,
            },
            `rank-${rank}`,
            themeClass,
        ]"
        :style="containerStyle"
        @click="handleClick">
        <!-- 排名区域 -->
        <div
            class="rank-section"
            :class="rankSectionClass">
            <slot
                name="rank"
                :rank="rank"
                :is-top3="isTop3"
                :rank-display="rankDisplay">
                <div class="rank-content">
                    <!-- TOP3特殊图标 -->
                    <img
                        v-if="isTop3 && rankIcon"
                        :src="rankIcon"
                        :alt="`第${rank}名`"
                        class="rank-icon"
                    />
                    <!-- 普通排名数字 -->
                    <span
                        v-else
                        class="rank-number"
                        :class="rankNumberClass">
                        {{ rankDisplay }}
                    </span>
                </div>
            </slot>
        </div>

        <!-- 头像区域 -->
        <div
            class="avatar-section"
            :class="avatarSectionClass">
            <slot
                name="avatar"
                :user-info="userInfo"
                :is-empty="isEmpty">
                <div
                    class="avatar-container"
                    :class="avatarContainerClass"
                    @click.stop="handleAvatarClick">
                    <img
                        :src="getAvatar(userInfo?.username)"
                        :alt="userInfo?.nickname || '头像'"
                        class="avatar-image"
                        :class="avatarImageClass"
                        @error="handleAvatarError"
                    />

                    <!-- 房间状态 -->
                    <div
                        v-if="showRoomStatus && channelStatus"
                        class="room-status"
                        @click.stop="handleRoomClick">
                        <slot
                            name="roomStatus"
                            :channel-status="channelStatus"
                            :channel-info="channelInfo">
                            <room-status
                                :status="channelStatus"
                                :cid="channelInfo?.channelId"
                            ></room-status>
                        </slot>
                    </div>
                </div>
            </slot>
        </div>

        <!-- 用户信息区域 -->
        <div
            class="user-info-section"
            :class="userInfoSectionClass">
            <slot
                name="userInfo"
                :user-info="userInfo"
                :nickname="nickname"
                :is-empty="isEmpty">
                <div class="user-info-content">
                    <!-- 昵称 -->
                    <div
                        class="nickname"
                        :class="nicknameClass"
                        :title="userInfo?.nickname">
                        {{ nickname }}
                    </div>

                    <!-- 额外信息 -->
                    <div
                        v-if="showExtraInfo && extraInfo"
                        class="extra-info"
                        :class="extraInfoClass">
                        {{ extraInfo }}
                    </div>
                </div>
            </slot>
        </div>

        <!-- 数值区域 -->
        <div
            class="value-section"
            :class="valueSectionClass">
            <slot
                name="value"
                :value="value"
                :formatted-value="formattedValue"
                :value-label="valueLabel"
                :is-empty="isEmpty">
                <div class="value-content">
                    <!-- 数值标签 -->
                    <div
                        v-if="valueLabel"
                        class="value-label"
                        :class="valueLabelClass">
                        {{ valueLabel }}
                    </div>

                    <!-- 数值 -->
                    <div
                        class="value-number"
                        :class="valueNumberClass">
                        {{ formattedValue }}
                    </div>

                    <!-- 额外内容区域 -->
                    <div
                        v-if="$slots.extra"
                        class="extra-section"
                        :class="extraSectionClass">
                        <slot
                            name="extra"
                            :data="data"
                            :rank="rank"
                            :is-me="isMe" />
                    </div>

                    <!-- 差距信息（仅个人排名显示） -->
                    <div
                        v-if="isMe && showDiff && diffInfo"
                        class="diff-info"
                        :class="diffInfoClass">
                        <div
                            v-if="diffInfo.ltPrev"
                            class="diff-prev">
                            {{ diffInfo.ltPrev }}
                        </div>
                        <div
                            v-if="diffInfo.gtNext"
                            class="diff-next">
                            {{ diffInfo.gtNext }}
                        </div>
                    </div>
                </div>
            </slot>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { getAvatar, omitValue, safeOmitTxt, toPerson, toRoom } from '@/utils/index';

const props = defineProps({
    // 排名数据
    data: {
        type: Object,
        required: true,
    },

    // 排名
    rank: {
        type: [Number, String],
        default: 0,
    },

    // 是否为当前用户
    isMe: {
        type: Boolean,
        default: false,
    },

    // 是否为空项目
    isEmpty: {
        type: Boolean,
        default: false,
    },

    // 数值标签
    valueLabel: {
        type: String,
        default: '',
    },

    // 是否可点击
    clickable: {
        type: Boolean,
        default: true,
    },

    // 是否显示房间状态
    showRoomStatus: {
        type: Boolean,
        default: true,
    },

    // 是否显示额外信息
    showExtraInfo: {
        type: Boolean,
        default: false,
    },

    // 是否显示差距信息
    showDiff: {
        type: Boolean,
        default: false,
    },

    // TOP3排名图标映射
    topRankIcons: {
        type: Object,
        default: () => ({
            1: '/rank-icons/top1.png',
            2: '/rank-icons/top2.png',
            3: '/rank-icons/top3.png',
        }),
    },

    // 主题样式
    theme: {
        type: String,
        default: 'default',
        validator: value => ['default', 'dark', 'light', 'colorful'].includes(value),
    },

    // 样式类
    containerClass: {
        type: [String, Array, Object],
        default: '',
    },

    containerStyle: {
        type: [String, Object],
        default: '',
    },

    rankSectionClass: {
        type: [String, Array, Object],
        default: '',
    },

    avatarSectionClass: {
        type: [String, Array, Object],
        default: '',
    },

    userInfoSectionClass: {
        type: [String, Array, Object],
        default: '',
    },

    valueSectionClass: {
        type: [String, Array, Object],
        default: '',
    },

    extraSectionClass: {
        type: [String, Array, Object],
        default: '',
    },
});

const emit = defineEmits([
    'click', // 点击排名项
    'avatarClick', // 点击头像
    'roomClick', // 点击房间状态
]);

// 用户信息
const userInfo = computed(() => props.data?.userInfo || {});

// 头像
const avatar = computed(() => {
    if (props.isEmpty)
        return '';

    return getAvatar(userInfo.value?.username);
});

// 昵称
const nickname = computed(() => {
    if (props.isEmpty)
        return '虚位以待';

    const name = userInfo.value?.nickname || '未知用户';

    return safeOmitTxt(name, 10);
});

// 数值
const value = computed(() => props.data?.value || 0);

// 格式化数值
const formattedValue = computed(() => {
    if (props.isEmpty)
        return '0';

    return omitValue(value.value);
});

// 是否为TOP3
const isTop3 = computed(() => props.rank >= 1 && props.rank <= 3);

// 排名显示
const rankDisplay = computed(() => {
    if (props.rank === 0)
        return '';
    return props.data?.rankHuman || props.rank.toString();
});

// TOP3排名图标
const rankIcon = computed(() => {
    if (!isTop3.value)
        return '';
    return props.topRankIcons[props.rank] || '';
});

// 房间状态
const channelStatus = computed(() => props.data?.channelInfo?.status || '');

// 频道信息
const channelInfo = computed(() => props.data?.channelInfo || {});

// 额外信息
const extraInfo = computed(() => props.data?.extraInfo || '');

// 差距信息
const diffInfo = computed(() => {
    if (!props.isMe)
        return null;

    return {
        ltPrev: props.data?.ltPrevValueHuman || '',
        gtNext: props.data?.gtNextValueHuman || '',
    };
});

// 主题样式类
const themeClass = computed(() => `rank-item--${props.theme}`);

// 动态样式类
const rankNumberClass = computed(() => ({
    'rank-number--top3': isTop3.value,
    'rank-number--me': props.isMe,
}));

const avatarContainerClass = computed(() => ({
    'avatar-container--top3': isTop3.value,
    'avatar-container--me': props.isMe,
    'avatar-container--empty': props.isEmpty,
}));

const avatarImageClass = computed(() => ({
    'avatar-image--top3': isTop3.value,
    'avatar-image--me': props.isMe,
}));

const nicknameClass = computed(() => ({
    'nickname--top3': isTop3.value,
    'nickname--me': props.isMe,
    'nickname--empty': props.isEmpty,
}));

const valueNumberClass = computed(() => ({
    'value-number--top3': isTop3.value,
    'value-number--me': props.isMe,
}));

const valueLabelClass = computed(() => ({
    'value-label--small': props.valueLabel && props.valueLabel.length > 4,
}));

const diffInfoClass = computed(() => ({
    'diff-info--visible': props.showDiff && diffInfo.value,
}));

const extraInfoClass = computed(() => ({
    'extra-info--visible': props.showExtraInfo && extraInfo.value,
}));

/**
 * 处理点击事件
 */
function handleClick() {
    if (props.clickable && !props.isEmpty) {
        emit('click', {
            data: props.data,
            rank: props.rank,
            isMe: props.isMe,
        });
    }
}

/**
 * 处理头像点击
 */
function handleAvatarClick() {
    if (!props.isEmpty && userInfo.value?.username) {
        emit('avatarClick', {
            username: userInfo.value.username,
            userInfo: userInfo.value,
        });

        toPerson(userInfo.value.username);
    }
}

/**
 * 处理房间点击
 */
function handleRoomClick() {
    if (channelInfo.value?.channelId) {
        emit('roomClick', {
            channelId: channelInfo.value.channelId,
            channelInfo: channelInfo.value,
        });

        toRoom(channelInfo.value.channelId);
    }
}

/**
 * 处理头像加载错误
 */
function handleAvatarError(event) {
    // 设置默认头像
    event.target.src = '/default-avatar.png';
}

// 对外暴露的方法
defineExpose({
    // 状态
    userInfo,
    avatar,
    nickname,
    value,
    formattedValue,
    isTop3,
    rankDisplay,

    // 方法
    handleClick,
    handleAvatarClick,
    handleRoomClick,
});
</script>

<style scoped lang="less">
.rank-item {
    display: flex;
    align-items: center;
    width: 337px;
    height: 83.5px;
    box-sizing: border-box;
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100%;
    padding: 0 6px;

    &.is-me {
        width: 375px;
        height: 96.5px;
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 100;
        background-image: url('@/assets/img/<EMAIL>');
    }

    &.is-top3 {
        .rank-section {
            .rank-icon {
                width: 24px;
                height: 24px;
            }
        }
    }

    &.is-empty {
        opacity: 0.6;

        .avatar-image {
            background: rgba(255, 255, 255, 0.1);
        }
    }
}

.rank-section {
    flex-shrink: 0;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rank-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.rank-number {
    font-size: 15px;
    font-weight: 500;
    color: #b89a9a;

    &.rank-number--top3 {
        color: #b89a9a;
        font-weight: 500;
    }

    &.rank-number--me {
        color: #ffffff;
    }
}

.rank-icon {
    width: 20px;
    height: 20px;
    object-fit: contain;
}

.avatar-section {
    flex-shrink: 0;
    margin: 0 12px;
}

.avatar-container {
    position: relative;
    width: 54px;
    height: 54px;
    border-radius: 50%;
    border: 1px solid #feef9a;

    &.avatar-container--top3 {
        width: 50px;
        height: 50px;
        border: 1px solid #ffd700;
    }

    &.avatar-container--me {
    }
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    cursor: pointer;
}

.room-status {
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
}

.user-info-section {
    flex: 1;
    min-width: 0;
}

.user-info-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.nickname {
    font-size: 14px;
    color: #ffeaea;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.nickname--top3 {
        color: #ffd700;
    }

    &.nickname--me {
    }

    &.nickname--empty {
        color: rgba(255, 255, 255, 0.5);
        font-style: italic;
    }
}

.extra-info {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.value-section {
    flex-shrink: 0;
    text-align: right;
    min-width: 60px;
}

.value-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.value-label {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.6);

    &.value-label--small {
        font-size: 9px;
    }
}

.value-number {
    font-size: 14px;
    font-weight: 500;
    color: #ffec6f;

    &.value-number--top3 {
        color: #ffd700;
        font-size: 16px;
    }

    &.value-number--me {
    }
}

.diff-info {
    display: flex;
    flex-direction: column;
    gap: 1px;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.5);
}

.extra-section {
    flex-shrink: 0;
    margin-left: 8px;
}

// 主题样式
.rank-item--dark {
    background: rgba(0, 0, 0, 0.3);

    &.is-me {
        background: rgba(0, 0, 0, 0.5);
        border-color: rgba(255, 255, 255, 0.3);
    }
}

.rank-item--light {
    background: rgba(255, 255, 255, 0.1);

    &.is-me {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(0, 0, 0, 0.2);
    }
}

.rank-item--colorful {
    &.rank-1 {
        background: linear-gradient(90deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    }

    &.rank-2 {
        background: linear-gradient(90deg, rgba(192, 192, 192, 0.2), rgba(192, 192, 192, 0.1));
    }

    &.rank-3 {
        background: linear-gradient(90deg, rgba(205, 127, 50, 0.2), rgba(205, 127, 50, 0.1));
    }
}

// 响应式适配
@media (max-width: 375px) {
    .rank-item {
        padding: 6px 8px;
    }

    .rank-section {
        width: 32px;
    }

    .avatar-container {
        width: 36px;
        height: 36px;

        &.avatar-container--top3 {
            width: 40px;
            height: 40px;
        }
    }

    .nickname {
        font-size: 13px;
    }

    .value-number {
        font-size: 13px;

        &.value-number--top3 {
            font-size: 14px;
        }
    }
}
</style>
