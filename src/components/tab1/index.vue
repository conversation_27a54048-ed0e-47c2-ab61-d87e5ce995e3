<template>
    <img
        v-if="initData.isDs"
        class="absolute right-[2px] top-[446px] h-[41.5px] w-[41.5px]"
        src="@/assets/img/<EMAIL>"
        @click="openTaskModal">
    <img
        src="@/assets/img/<EMAIL>"
        class="bg-default h-[161.5px] w-[375px]"
    >
    <div class="mt-[19px] text-[12px] text-[#E4C59D]">根据大神近期完单和转化数据分为3个赛区</div>
    <SubNavTabs
        v-model="subNav"
        class="mt-[20px]"
        @change="handleSubNavChange"
    />

    <div class="r-rank-bg mt-[-27px] pt-[30px]">
        <van-list
            v-model:loading="loading"
            :finished="!rankStore.hasMore"
            offset="100"
            @load="handleLoadMore"
        >
            <RankList
                :list="rankStore.list"
                :self-data="rankStore.selfData"
                :show-top="true"
                :show-normal="true"
                :show-self="true"
                :top-component="RankTop"
                :normal-component="RankItem"
                :self-component="RankItem"
            >
                <!-- 为每个TOP项添加额外内容 -->
                <template #extra="{ data, rank }">
                    <div
                        class="extra-content mt-[8px]"
                        :class="`rank-${rank}-extra`">
                        <template v-if="data.money">
                            <div class="money-info">
                                <span class="money-value">预计得{{ formatMoney(data.money) }}</span>
                            </div>
                        </template>
                    </div>
                </template>
            </RankList>
        </van-list>
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { createRankListStore } from './rank-list/use-rank-list.js';
import RankTop from './rank-top/rank-top.vue';
import RankItem from './rank-item/rank-item.vue';
import { getRank as getRankList } from '@/api';
import { useSubNavigation } from '@/hooks/useSubNavigation';
import { SUB_NAV_CONFIG } from '@/components/common/constants';

const refreshing = ref(false);
const loading = ref(false);

const openTaskModal = () => {
    useEventBus('task-modal').emit({ show: true });
};
const { subNav, changeSubNav } = useSubNavigation(1, SUB_NAV_CONFIG);

// 创建排行榜store
const rankStore = createRankListStore('activityRank', {
    apiFunction: getRankList,
    pageSize: 20,
    maxItems: 100,
})();

// 格式化金额
const formatMoney = (money) => {
    return omitValue(money);
};

// 加载更多
const handleLoadMore = async () => {
    if (rankStore.isLoading || !rankStore.hasMore) {
        loading.value = false;
        return;
    }
    try {
        await rankStore.loadMore({
            uid: myWebview.params.uid,
            raceId: subNav.value,
        });
    }
    finally {
        loading.value = false;
    }
};

// 刷新排行榜
const handleRefresh = async () => {
    try {
        await rankStore.loadData({
            uid: myWebview.params.uid,
            raceId: subNav.value,
            page: 1,
            size: 20,
        });
    }
    finally {
        refreshing.value = false;
    }
};

const handleSubNavChange = async (value) => {
    await changeSubNav(value, handleRefresh);
};

onMounted(async () => {
    await handleRefresh();
});
</script>

<style lang="less" scoped>
.r-rank-bg {
    .point-9-px(url('@/assets/img/<EMAIL>'),375, 656, 300, 0, 100, 0 , 4);
    height: auto;
    border-image-repeat: stretch;
    border-top: 0;
    border-bottom: 0;
    border-left: 0;
    border-right: 0;
    width: 375px;
    min-height: 656px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
}

// 额外内容样式
.extra-content {
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100%;
    width: 98.5px;
    height: 19.5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4b0d0d;
    font-size: 11px;
}
</style>
