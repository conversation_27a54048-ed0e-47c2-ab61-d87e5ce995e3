/**
 * common-components/rank-top/rank-config.js
 * TOP排名配置常量和工具函数
 */

/**
 * TOP排名主题
 */
export const RANK_TOP_THEMES = {
    DEFAULT: 'default',
    GOLDEN: 'golden',
    COLORFUL: 'colorful',
    ELEGANT: 'elegant',
};

/**
 * TOP排名尺寸
 */
export const RANK_TOP_SIZES = {
    SMALL: 'small',
    MEDIUM: 'medium',
    LARGE: 'large',
};

/**
 * 默认图片资源配置
 */
export const DEFAULT_RANK_IMAGES = {
    // 第一名资源
    1: {
        background: '/rank-top/bg-top1.png',
        crown: '/rank-top/crown-gold.png',
        avatarBorder: '/rank-top/avatar-border-gold.png',
        nicknameBackground: '/rank-top/nickname-bg-gold.png',
        valueBackground: '/rank-top/value-bg-gold.png',
        rankIcon: '/rank-top/rank-icon-1.png',
        decoration: '/rank-top/decoration-gold.png',
    },
    // 第二名资源
    2: {
        background: '/rank-top/bg-top2.png',
        crown: '/rank-top/crown-silver.png',
        avatarBorder: '/rank-top/avatar-border-silver.png',
        nicknameBackground: '/rank-top/nickname-bg-silver.png',
        valueBackground: '/rank-top/value-bg-silver.png',
        rankIcon: '/rank-top/rank-icon-2.png',
        decoration: '/rank-top/decoration-silver.png',
    },
    // 第三名资源
    3: {
        background: '/rank-top/bg-top3.png',
        crown: '/rank-top/crown-bronze.png',
        avatarBorder: '/rank-top/avatar-border-bronze.png',
        nicknameBackground: '/rank-top/nickname-bg-bronze.png',
        valueBackground: '/rank-top/value-bg-bronze.png',
        rankIcon: '/rank-top/rank-icon-3.png',
        decoration: '/rank-top/decoration-bronze.png',
    },
};

/**
 * 图片资源样式集合
 */
export const RANK_IMAGE_STYLES = {
    // 皇冠样式
    CROWN_STYLE: {
        1: { background: '/rank-top/crown-gold.png', crown: '/rank-top/crown-gold.png' },
        2: { background: '/rank-top/crown-silver.png', crown: '/rank-top/crown-silver.png' },
        3: { background: '/rank-top/crown-bronze.png', crown: '/rank-top/crown-bronze.png' },
    },

    // 奖牌样式
    MEDAL_STYLE: {
        1: { background: '/rank-top/medal-gold.png', crown: '/rank-top/medal-gold.png' },
        2: { background: '/rank-top/medal-silver.png', crown: '/rank-top/medal-silver.png' },
        3: { background: '/rank-top/medal-bronze.png', crown: '/rank-top/medal-bronze.png' },
    },

    // 奖杯样式
    TROPHY_STYLE: {
        1: { background: '/rank-top/trophy-gold.png', crown: '/rank-top/trophy-gold.png' },
        2: { background: '/rank-top/trophy-silver.png', crown: '/rank-top/trophy-silver.png' },
        3: { background: '/rank-top/trophy-bronze.png', crown: '/rank-top/trophy-bronze.png' },
    },

    // 简约样式
    SIMPLE_STYLE: {
        1: { background: '', crown: '', rankIcon: '👑' },
        2: { background: '', crown: '', rankIcon: '🥈' },
        3: { background: '', crown: '', rankIcon: '🥉' },
    },
};

/**
 * TOP排名预设配置
 */
export const RANK_TOP_PRESETS = {
    // 默认配置
    DEFAULT: {
        theme: RANK_TOP_THEMES.DEFAULT,
        size: RANK_TOP_SIZES.MEDIUM,
        showCrown: true,
        showDecoration: true,
        showRoomStatus: true,
        showUserId: false,
        clickable: true,
        imageStyle: 'CROWN_STYLE',
    },

    // 简洁配置
    SIMPLE: {
        theme: RANK_TOP_THEMES.DEFAULT,
        size: RANK_TOP_SIZES.SMALL,
        showCrown: false,
        showDecoration: false,
        showRoomStatus: false,
        showUserId: false,
        clickable: false,
        imageStyle: 'SIMPLE_STYLE',
    },

    // 华丽配置
    LUXURY: {
        theme: RANK_TOP_THEMES.GOLDEN,
        size: RANK_TOP_SIZES.LARGE,
        showCrown: true,
        showDecoration: true,
        showRoomStatus: true,
        showUserId: true,
        clickable: true,
        imageStyle: 'CROWN_STYLE',
    },

    // 竞赛配置
    COMPETITION: {
        theme: RANK_TOP_THEMES.COLORFUL,
        size: RANK_TOP_SIZES.MEDIUM,
        showCrown: true,
        showDecoration: true,
        showRoomStatus: true,
        showUserId: false,
        clickable: true,
        imageStyle: 'TROPHY_STYLE',
    },

    // 优雅配置
    ELEGANT: {
        theme: RANK_TOP_THEMES.ELEGANT,
        size: RANK_TOP_SIZES.MEDIUM,
        showCrown: false,
        showDecoration: true,
        showRoomStatus: true,
        showUserId: false,
        clickable: true,
        imageStyle: 'MEDAL_STYLE',
    },
};

/**
 * 尺寸配置
 */
export const SIZE_CONFIGS = {
    [RANK_TOP_SIZES.SMALL]: {
        width: 120,
        height: 160,
        avatarSize: 60,
        fontSize: {
            nickname: 11,
            value: 12,
            rank: 14,
        },
    },
    [RANK_TOP_SIZES.MEDIUM]: {
        width: 150,
        height: 200,
        avatarSize: 80,
        fontSize: {
            nickname: 13,
            value: 14,
            rank: 16,
        },
    },
    [RANK_TOP_SIZES.LARGE]: {
        width: 180,
        height: 240,
        avatarSize: 100,
        fontSize: {
            nickname: 15,
            value: 16,
            rank: 18,
        },
    },
};

/**
 * 排名颜色配置
 */
export const RANK_COLORS = {
    1: {
        primary: '#FFD700',
        secondary: '#FFA500',
        textShadow: '#ff7b8d',
        gradient: 'linear-gradient(135deg, #FFD700, #FFA500)',
    },
    2: {
        primary: '#C0C0C0',
        secondary: '#A0A0A0',
        textShadow: '#5f82ca',
        gradient: 'linear-gradient(135deg, #C0C0C0, #A0A0A0)',
    },
    3: {
        primary: '#CD7F32',
        secondary: '#B8860B',
        textShadow: '#e48b7d',
        gradient: 'linear-gradient(135deg, #CD7F32, #B8860B)',
    },
};

/**
 * 创建TOP排名配置
 * @param {object} options - 配置选项
 * @returns {object} 完整配置
 */
export function createRankTopConfig(options = {}) {
    const {
        preset = 'DEFAULT',
        theme,
        size,
        showCrown,
        showDecoration,
        showRoomStatus,
        showUserId,
        clickable,
        imageStyle,
        customImages = {},
        ...rest
    } = options;

    const baseConfig = RANK_TOP_PRESETS[preset] || RANK_TOP_PRESETS.DEFAULT;

    return {
        ...baseConfig,
        ...(theme && { theme }),
        ...(size && { size }),
        ...(typeof showCrown === 'boolean' && { showCrown }),
        ...(typeof showDecoration === 'boolean' && { showDecoration }),
        ...(typeof showRoomStatus === 'boolean' && { showRoomStatus }),
        ...(typeof showUserId === 'boolean' && { showUserId }),
        ...(typeof clickable === 'boolean' && { clickable }),
        ...(imageStyle && { imageStyle }),
        customImages,
        ...rest,
    };
}

/**
 * 获取排名图片资源
 * @param {number} rank - 排名
 * @param {string} style - 图片样式
 * @param {object} customImages - 自定义图片
 * @returns {object} 图片资源配置
 */
export function getRankImages(rank, style = 'CROWN_STYLE', customImages = {}) {
    const defaultImages = DEFAULT_RANK_IMAGES[rank] || DEFAULT_RANK_IMAGES[1];
    const styleImages = RANK_IMAGE_STYLES[style]?.[rank] || {};

    return {
        ...defaultImages,
        ...styleImages,
        ...customImages,
    };
}

/**
 * 获取排名颜色
 * @param {number} rank - 排名
 * @returns {object} 颜色配置
 */
export function getRankColors(rank) {
    return RANK_COLORS[rank] || RANK_COLORS[1];
}

/**
 * 获取尺寸配置
 * @param {string} size - 尺寸
 * @returns {object} 尺寸配置
 */
export function getSizeConfig(size) {
    return SIZE_CONFIGS[size] || SIZE_CONFIGS[RANK_TOP_SIZES.MEDIUM];
}

/**
 * 验证TOP排名数据
 * @param {object} data - 排名数据
 * @param {number} rank - 排名
 * @returns {boolean} 是否有效
 */
export function validateRankTopData(data, rank) {
    if (!data || typeof data !== 'object') {
        return false;
    }

    if (typeof rank !== 'number' || rank < 1 || rank > 3) {
        return false;
    }

    // 检查必要字段
    const hasUserInfo = data.userInfo && typeof data.userInfo === 'object';
    const hasValue = typeof data.value === 'number';

    return hasUserInfo && hasValue;
}

/**
 * 标准化TOP排名数据
 * @param {object} data - 原始数据
 * @param {number} rank - 排名
 * @returns {object} 标准化后的数据
 */
export function normalizeRankTopData(data, rank) {
    if (!data)
        return null;

    return {
        rank,
        value: data.value || 0,
        userInfo: {
            id: data.userInfo?.id || data.id,
            username: data.userInfo?.username || data.username,
            nickname: data.userInfo?.nickname || data.nickname || '未知用户',
            avatar: data.userInfo?.avatar || data.avatar || '',
            alias: data.userInfo?.alias || data.alias || '',
            ...data.userInfo,
        },
        channelInfo: data.channelInfo || {},
        extraInfo: data.extraInfo || '',
        valueHuman: data.valueHuman || '',
        ...data,
    };
}

/**
 * 生成虚位以待TOP数据
 * @param {number} rank - 排名
 * @returns {object} 虚位以待数据
 */
export function generatePlaceholderTopData(rank) {
    return {
        rank,
        value: 0,
        isEmpty: true,
        userInfo: {
            id: `placeholder_top_${rank}`,
            username: '',
            nickname: '虚位以待',
            avatar: '',
            alias: '',
        },
        channelInfo: {},
        extraInfo: '',
        valueHuman: '0',
    };
}

/**
 * 创建TOP3布局配置
 * @param {Array} topList - TOP3数据列表
 * @param {object} options - 布局选项
 * @returns {object} 布局配置
 */
export function createTop3Layout(topList = [], options = {}) {
    const {
        arrangement = 'center', // center, left, podium
        spacing = 'normal', // tight, normal, loose
        alignment = 'bottom', // top, center, bottom
    } = options;

    const layouts = {
        center: {
            order: [2, 1, 3], // 第二名、第一名、第三名
            positions: ['left', 'center', 'right'],
        },
        left: {
            order: [1, 2, 3], // 按排名顺序
            positions: ['left', 'center', 'right'],
        },
        podium: {
            order: [2, 1, 3], // 领奖台布局
            positions: ['left', 'center', 'right'],
            heights: ['medium', 'high', 'low'],
        },
    };

    const spacingMap = {
        tight: '4px',
        normal: '8px',
        loose: '16px',
    };

    return {
        arrangement: layouts[arrangement] || layouts.center,
        spacing: spacingMap[spacing] || spacingMap.normal,
        alignment,
        itemCount: Math.min(topList.length, 3),
    };
}

/**
 * 获取房间状态文本
 * @param {string} status - 房间状态
 * @returns {string} 状态文本
 */
export function getRoomStatusText(status) {
    const statusMap = {
        active: '直播中',
        live: '直播中',
        online: '在线',
        offline: '离线',
        busy: '忙碌',
        away: '离开',
    };

    return statusMap[status] || status || '';
}

/**
 * 计算TOP排名布局
 * @param {Array} topList - TOP3数据
 * @param {string} layoutType - 布局类型
 * @returns {Array} 布局后的数据
 */
export function calculateTopLayout(topList, layoutType = 'center') {
    if (!Array.isArray(topList) || topList.length === 0) {
        return [];
    }

    const layout = createTop3Layout(topList, { arrangement: layoutType });
    const { order, positions } = layout.arrangement;

    return order.map((rank, index) => {
        const item = topList.find(item => item.rank === rank)
                    || generatePlaceholderTopData(rank);

        return {
            ...item,
            layoutPosition: positions[index],
            layoutIndex: index,
        };
    });
}

/**
 * 活动TOP排名配置
 */
export const ACTIVITY_RANK_TOP_CONFIG = createRankTopConfig({
    preset: 'LUXURY',
    imageStyle: 'CROWN_STYLE',
});

/**
 * 竞赛TOP排名配置
 */
export const COMPETITION_RANK_TOP_CONFIG = createRankTopConfig({
    preset: 'COMPETITION',
    imageStyle: 'TROPHY_STYLE',
});

/**
 * 简洁TOP排名配置
 */
export const SIMPLE_RANK_TOP_CONFIG = createRankTopConfig({
    preset: 'SIMPLE',
    imageStyle: 'SIMPLE_STYLE',
});
