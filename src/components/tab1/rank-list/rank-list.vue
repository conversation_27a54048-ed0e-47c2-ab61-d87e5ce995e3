<!--
  common-components/rank-list/rank-list.vue
  排行榜列表容器组件 - 提供统一的排行榜布局和数据管理
-->
<template>
    <div
        class="rank-list"
        :class="[selfData && 'mb-[100px]', ...containerClass]"
    >
        <!-- 排行榜内容 -->
        <template v-if="hasData">
            <!-- TOP3 特殊展示区域 -->
            <div
                v-if="showTop && top3List.length > 0"
                class="rank-top-section"
                :class="topSectionClass">
                <slot
                    name="top"
                    :list="top3List"
                    :is-empty="isTop3Empty">
                    <div class="rank-top-container">
                        <component
                            :is="topComponent"
                            v-for="(item, index) in top3List"
                            :key="`top-${index}`"
                            class="top-item"
                            :data="item"
                            :rank="index + 1"
                            :index="index"
                            :is-empty="!item || isEmptyItem(item)"
                            v-bind="topComponentProps">
                            <template
                                v-for="(_, slot) in $slots"
                                #[slot]="slotProps">
                                <slot
                                    :name="slot"
                                    v-bind="slotProps" />
                            </template>
                        </component>
                    </div>
                </slot>
            </div>

            <!-- 分隔线或提示信息 -->
            <div
                v-if="showTop && showSeparator && separatorText"
                class="rank-separator"
                :class="separatorClass">
                <slot
                    name="separator"
                    :text="separatorText">
                    {{ separatorText }}
                </slot>
            </div>

            <!-- 普通排名列表区域 -->
            <div
                v-if="showNormal && normalList.length > 0"
                class="rank-normal-section"
                :class="normalSectionClass">
                <slot
                    name="normal"
                    :list="normalList"
                    :start-rank="normalStartRank">
                    <div class="rank-normal-container">
                        <component
                            :is="normalComponent"
                            v-for="(item, index) in normalList"
                            :key="`normal-${item.id || item.rank || index}`"
                            :data="item"
                            :rank="item.rank || normalStartRank + index"
                            :index="index"
                            :is-me="isMe(item)"
                            v-bind="normalComponentProps">
                            <template
                                v-for="(_, slot) in $slots"
                                #[slot]="slotProps">
                                <slot
                                    :name="slot"
                                    v-bind="slotProps" />
                            </template>
                        </component>
                    </div>
                </slot>
            </div>

            <!-- 加载更多 -->
            <div
                v-if="showLoadMore"
                class="rank-load-more"
                :class="loadMoreClass">
                <slot
                    name="loadMore"
                    :loading="isLoading"
                    :has-more="hasMore">
                    <div
                        v-if="isLoading"
                        class="loading-text">
                        {{ loadingText }}
                    </div>
                    <div
                        v-else-if="hasMore"
                        class="load-more-text">
                        {{ loadMoreText }}
                    </div>
                    <div
                        v-else
                        class="no-more-text">
                        {{ noMoreText }}
                    </div>
                </slot>
            </div>
        </template>

        <!-- 空数据状态 -->
        <div
            v-else
            class="rank-empty"
            :class="emptyClass">
            <slot
                name="empty"
                :is-empty="isEmpty">
                <div class="empty-content">
                    <div class="empty-icon">🏆</div>
                    <div class="empty-text">{{ emptyText }}</div>
                </div>
            </slot>
        </div>

        <!-- 个人排名 -->
        <div
            v-if="showSelf && selfData"
            class="rank-self-section"
            :class="selfSectionClass">
            <slot
                name="self"
                :data="selfData"
                :rank="selfData.rank">
                <component
                    :is="selfComponent"
                    :data="selfData"
                    :rank="selfData.rank"
                    :is-me="true"
                    v-bind="selfComponentProps">
                    <template
                        v-for="(_, slot) in $slots"
                        #[slot]="slotProps">
                        <slot
                            :name="slot"
                            v-bind="slotProps" />
                    </template>
                </component>
            </slot>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    // 排行榜数据
    list: {
        type: Array,
        default: () => [],
    },

    // 个人数据
    selfData: {
        type: Object,
        default: null,
    },

    // 是否显示TOP3区域
    showTop: {
        type: Boolean,
        default: true,
    },

    // 是否显示普通排名区域
    showNormal: {
        type: Boolean,
        default: true,
    },

    // 是否显示个人排名
    showSelf: {
        type: Boolean,
        default: true,
    },

    // 是否显示分隔线
    showSeparator: {
        type: Boolean,
        default: false,
    },

    // 分隔线文本
    separatorText: {
        type: String,
        default: '',
    },

    // TOP组件
    topComponent: {
        type: [String, Object],
        default: 'div',
    },

    // 普通排名组件
    normalComponent: {
        type: [String, Object],
        default: 'div',
    },

    // 个人排名组件
    selfComponent: {
        type: [String, Object],
        default: 'div',
    },

    // TOP组件属性
    topComponentProps: {
        type: Object,
        default: () => ({}),
    },

    // 普通排名组件属性
    normalComponentProps: {
        type: Object,
        default: () => ({}),
    },

    // 个人排名组件属性
    selfComponentProps: {
        type: Object,
        default: () => ({}),
    },

    // 当前用户ID（用于高亮个人排名）
    currentUserId: {
        type: [String, Number],
        default: null,
    },

    // 空数据文本
    emptyText: {
        type: String,
        default: '暂无数据',
    },

    // 加载状态
    isLoading: {
        type: Boolean,
        default: false,
    },

    // 是否还有更多数据
    hasMore: {
        type: Boolean,
        default: false,
    },

    // 是否显示加载更多
    showLoadMore: {
        type: Boolean,
        default: false,
    },

    // 加载中文本
    loadingText: {
        type: String,
        default: '加载中...',
    },

    // 加载更多文本
    loadMoreText: {
        type: String,
        default: '下拉加载更多',
    },

    // 没有更多文本
    noMoreText: {
        type: String,
        default: '没有更多了',
    },

    // 样式类
    containerClass: {
        type: [String, Array, Object],
        default: '',
    },

    topSectionClass: {
        type: [String, Array, Object],
        default: '',
    },

    normalSectionClass: {
        type: [String, Array, Object],
        default: '',
    },

    selfSectionClass: {
        type: [String, Array, Object],
        default: '',
    },

    separatorClass: {
        type: [String, Array, Object],
        default: '',
    },

    emptyClass: {
        type: [String, Array, Object],
        default: '',
    },

    loadMoreClass: {
        type: [String, Array, Object],
        default: '',
    },
});

const emit = defineEmits([
    'loadMore', // 加载更多
    'itemClick', // 点击排名项
    'topClick', // 点击TOP项
    'selfClick', // 点击个人排名
]);

// 是否有数据
const hasData = computed(() => {
    return props.list && props.list.length > 0;
});

// 是否为空
const isEmpty = computed(() => {
    return !hasData.value;
});

// TOP3列表
const top3List = computed(() => {
    if (!hasData.value)
        return [];

    const top3 = props.list.slice(0, 3);

    // 补齐3个位置，空位用占位数据
    const result = Array.from({ length: 3 }, (_, index) => {
        return (
            top3[index] || {
                rank: index + 1,
                isEmpty: true,
                userInfo: {
                    nickname: '虚位以待',
                    avatar: '',
                },
                value: 0,
            }
        );
    });

    return result;
});

// 普通排名列表（第4名开始）
const normalList = computed(() => {
    if (!hasData.value)
        return [];
    return props.list.slice(3);
});

// 普通排名起始排名
const normalStartRank = computed(() => {
    return 4;
});

// TOP3是否为空
const isTop3Empty = computed(() => {
    return top3List.value.every(item => isEmptyItem(item));
});

/**
 * 检查是否为空项目
 * @param {object} item - 排名项
 * @returns {boolean} 是否为空
 */
function isEmptyItem(item) {
    return (
        !item || item.isEmpty || !item.userInfo?.nickname || item.userInfo?.nickname === '虚位以待'
    );
}

/**
 * 检查是否为当前用户
 * @param {object} item - 排名项
 * @returns {boolean} 是否为当前用户
 */
function isMe(item) {
    if (!props.currentUserId || !item?.userInfo?.id) {
        return false;
    }
    return String(item.userInfo.id) === String(props.currentUserId);
}

// 对外暴露的方法
defineExpose({
    // 状态
    hasData,
    isEmpty,
    top3List,
    normalList,
    isTop3Empty,

    // 方法
    isEmptyItem,
    isMe,
});
</script>

<style scoped lang="less">
.rank-list {
    width: 100%;
    box-sizing: border-box;
}

.rank-top-section {
    margin-bottom: 22px;
}

.rank-top-container {
    display: flex;
    justify-content: center;
    padding-top: 47px;

    .top-item {
        &:nth-child(1) {
            order: 2;
            margin: -27px 7px 0 7px;
        }

        &:nth-child(2) {
            order: 1;
        }

        &:nth-child(3) {
            order: 3;
        }
    }
}

.rank-separator {
    padding: 8px 16px;
    text-align: center;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin: 16px 0;
}

.rank-normal-section {
    margin-bottom: 16px;
}

.rank-normal-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.rank-self-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.rank-empty {
    padding: 40px 20px;
    text-align: center;
}

.empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.empty-icon {
    font-size: 32px;
    opacity: 0.5;
}

.empty-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.rank-load-more {
    padding: 16px;
    text-align: center;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.loading-text,
.load-more-text,
.no-more-text {
    line-height: 1.5;
}

// 响应式适配
@media (max-width: 375px) {
    .rank-top-container {
        gap: 4px;
    }

    .rank-normal-container {
        gap: 4px;
    }

    .rank-separator {
        margin: 12px 0;
        padding: 6px 12px;
        font-size: 11px;
    }
}
</style>
