/**
 * common-components/rank-list/use-rank-list.js
 * 排行榜列表Store - 提供统一的排行榜数据管理和分页加载
 */

import { defineStore } from 'pinia';
import { computed, onMounted, ref } from 'vue';

/**
 * 创建排行榜列表Store
 * @param {string} storeId - Store唯一标识
 * @param {object} options - 配置选项
 * @returns {Function} Store函数
 */
export function createRankListStore(storeId = 'rankList', options = {}) {
    const { pageSize = 20, maxItems = 100, autoLoad = false, enableSelf = true, apiFunction = null } = options;

    return defineStore(storeId, () => {
        // 排行榜列表数据
        const list = ref([]);
        // 个人排名数据
        const selfData = ref(null);
        // 分页信息
        const page = ref(1);
        const size = ref(pageSize);
        const total = ref(0);
        // 状态管理
        const isLoading = ref(false);
        const isLoadingMore = ref(false);
        const hasError = ref(false);
        const errorMessage = ref('');

        // 是否还有更多数据
        const hasMore = computed(() => {
            if (total.value === 0)
                return false;
            if (list.value.length >= maxItems)
                return false;
            return list.value.length < total.value;
        });

        // 是否为空
        const isEmpty = computed(() => {
            return list.value.length === 0 && !isLoading.value;
        });

        // TOP3列表
        const top3List = computed(() => {
            return list.value.slice(0, 3);
        });

        // 普通排名列表（第4名开始）
        const normalList = computed(() => {
            return list.value.slice(3);
        });

        // 是否有数据
        const hasData = computed(() => {
            return list.value.length > 0;
        });

        /**
         * 设置排行榜数据
         * @param {Array} rankList - 排行榜数据
         * @param {object} pagination - 分页信息
         * @param {object} self - 个人数据
         */
        function setData(rankList = [], pagination = {}, self = null) {
            list.value = rankList;

            if (pagination.page !== undefined)
                page.value = pagination.page;
            if (pagination.size !== undefined)
                size.value = pagination.size;
            if (pagination.total !== undefined)
                total.value = pagination.total;

            if (enableSelf && self) {
                selfData.value = self;
            }

            hasError.value = false;
            errorMessage.value = '';
        }

        /**
         * 追加排行榜数据（用于分页加载）
         * @param {Array} rankList - 新的排行榜数据
         * @param {object} pagination - 分页信息
         */
        function appendData(rankList = [], pagination = {}) {
            if (Array.isArray(rankList) && rankList.length > 0) {
                // 去重处理，基于用户ID或排名
                const existingIds = new Set(
                    list.value.map(item => item.userInfo?.id || item.id || item.rank),
                );

                const newItems = rankList.filter((item) => {
                    const itemId = item.userInfo?.id || item.id || item.rank;
                    return !existingIds.has(itemId);
                });

                list.value.push(...newItems);
            }

            if (pagination.page !== undefined)
                page.value = pagination.page;
            if (pagination.total !== undefined)
                total.value = pagination.total;
        }

        function getCurrentParams() {
            return {
                page: page.value,
                size: size.value,
            };
        }

        function prepareNextPage() {
            const nextPage = page.value + 1;
            return {
                page: nextPage,
                size: size.value,
            };
        }

        /**
         * 加载排行榜数据
         * @param {object} params - 请求参数
         * @param {boolean} isLoadMore - 是否为加载更多
         * @returns {Promise} 请求Promise
         */
        async function loadData(params = {}, isLoadMore = false) {
            if (!apiFunction) {
                console.warn('No API function provided for rank list');
                return;
            }

            try {
                setLoading(true, isLoadMore);
                setError('');

                const requestParams = {
                    ...getCurrentParams(),
                    ...params,
                };

                const [response] = await apiFunction(requestParams);

                if (response && response.code === 0) {
                    const { list: newList = [], pagination = {}, self = null } = response.data || {};

                    if (isLoadMore) {
                        appendData(newList, pagination);
                    }
                    else {
                        setData(newList, pagination, self);
                    }
                }
                else {
                    throw new Error(response?.message || '请求失败');
                }
            }
            catch (error) {
                console.error('Load rank data error:', error);
                setError(error.message || '加载失败');
            }
            finally {
                setLoading(false, isLoadMore);
            }
        }

        /**
         * 刷新数据
         * @param {object} params - 请求参数
         * @returns {Promise} 请求Promise
         */
        async function refresh(params = {}) {
            reset();
            return loadData(params, false);
        }

        /**
         * 加载更多数据
         * @param {object} params - 请求参数
         * @returns {Promise} 请求Promise
         */
        async function loadMore(params = {}) {
            if (!hasMore.value || isLoadingMore.value) {
                return;
            }

            const nextPageParams = prepareNextPage();
            return loadData({ ...nextPageParams, ...params }, true);
        }

        function setLoading(loading, isMore = false) {
            if (isMore) {
                isLoadingMore.value = loading;
            }
            else {
                isLoading.value = loading;
            }
        }

        function setError(message = '') {
            hasError.value = !!message;
            errorMessage.value = message;
        }

        function reset() {
            list.value = [];
            selfData.value = null;
            page.value = 1;
            total.value = 0;
            isLoading.value = false;
            isLoadingMore.value = false;
            hasError.value = false;
            errorMessage.value = '';
        }

        // 自动加载
        if (autoLoad && apiFunction) {
            onMounted(() => {
                loadData();
            });
        }

        return {
            // 状态
            list,
            selfData,
            page,
            size,
            total,
            isLoading,
            isLoadingMore,
            hasError,
            errorMessage,

            // 计算属性
            hasMore,
            isEmpty,
            top3List,
            normalList,
            hasData,

            // 方法
            loadData,
            refresh,
            loadMore,
            reset,
            setError,
        };
    });
}

/**
 * 默认排行榜Store实例
 */
export const useRankList = createRankListStore('defaultRankList');

export default useRankList;
