<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div
            class="bg-default h-[315.5px] w-[279.5px] flex flex-col items-center pt-[60px]"
            :style="`background-image: url(${requireImg('<EMAIL>')})`"
        >
            <div class="text-[14px] text-[#E4C59D]">  大神每日完单人数达标可获得奖励 </div>
            <div class="mt-[4px] text-[12px] text-[#E4C59D]">今日完成单人数：{{ orderUserNum }}</div>
            <div class="mt-[38px] flex">
                <div
                    v-for="(reward, index) of rwdList"
                    :key="`reward-${index}`"
                    class="reward">
                    <div
                        class="reward-frame relative"
                    >
                        <div
                            v-if=" orderUserNum >= reward.num"
                            class="bg-default absolute right-[-8px] top-[-17px] h-[26px] w-[74px] flex pl-[23px] pt-[3px] text-[14px] text-[#70100E] font-bold font-italic"
                            :style="`background-image: url(${requireImg('<EMAIL>')})`"
                        >
                            已完成
                        </div>
                        <div class="bg-[linear-gradient(0deg,#3d0b0b 0%, #9d100c 100%)] h-[48px] w-[48px] border-[1px] border-[solid] rounded-[4.5px]">
                            <img
                                class="reward-img"
                                :src="getRewardInfo(reward?.id).imageUrl" />
                        </div>
                        <div class="name z-1 mt-[2px]">
                            <p
                                class="whitespace-nowrap"
                                v-html="
                                    getRewardName(reward, reward?.num)
                                "></p>
                        </div>
                    </div>
                    <div
                        class="flex-center bg-default mt-[6px] h-[19.5px] w-[73px] flex text-[12px] text-[#F0CE77] font-[400]"
                        :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }">
                        完单{{ reward.num }}人
                    </div>
                </div>
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { ref } from 'vue';
import { init } from '@/api';

const props = defineProps({
    type: {
        type: Number,
        default: 0,
    },
});

const rwdList = ref([
    { id: 'Add_Gift_1', num: 1 },
    { id: 'Add_Gift_2', num: 3 },
]);

const getRewardName = (reward, num) => {
    const rewardInfo = getRewardInfo(reward?.id);
    const mark = rewardInfo?.mark;
    if (!mark || mark === '其他') {
        return `${rewardInfo?.name}`;
    }
    return `${rewardInfo?.name}<br/>${(mark !== '包裹' ? mark : `${rewardInfo.price}豆`)}`;
};
const isShow = ref(false);
const orderUserNum = ref(0);

// 请求getTaskList 接口拿到数据合并入rewardList 中
const fetchTaskList = async () => {
    try {
        const [{ data }] = await init();
        const { orderUserNum, isDs } = data.initData;
        orderUserNum.value = orderUserNum;
    }
    catch (error) {
        console.error('获取任务列表失败:', error);
    }
};

useEventBus('task-modal').on((params) => {
    isShow.value = params.show;
    fetchTaskList();
});
const closeDialog = () => {
    isShow.value = false;
};
</script>

<style lang="less" scoped>
    .reward {
    display: flex;
    flex-direction: column;
    align-items: center;

    .name {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 11px;
        font-weight: 400;
        text-align: center;
        color: #efd6a1;
        line-height: 12px;
        letter-spacing: 0.24px;
    }
}

.reward-frame {
    position: relative;
    width: 90.5px;
    height: 105.5px;
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100% 100%;
    box-sizing: border-box;
    margin-right: 20px;
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 13px;
    .reward-img {
        width: 48px;
        height: 48px;
        object-fit: contain;
    }
}
</style>
