import { defineStore } from 'pinia';
import { NAV, SUB_NAV, TAB_ID, TAB_NAME } from './const';
import { track } from '@/utils/jsbridge';

const query = parseUrlQuery();

const useNav = defineStore('nav', () => {
    const current = ref(1);
    const subNav = ref(SUB_NAV[0].value);

    const changeNav = (value) => {
        if (current.value === value)
            return;
        track({ page_id: 'activity_page', event_id: 'tab_click', general_param_1: TAB_NAME[value] });
        subNav.value = SUB_NAV[0].value;
        current.value = value;
    };

    /**
     * 初始化导航函数
     * 本函数用于根据查询参数决定是否改变当前导航
     */
    const initNav = () => {

    };

    const changeSubNav = (value) => {
        subNav.value = value;
    };

    const isSend = computed(() => subNav.value === SUB_NAV[0].value);

    return {
        NAV,
        current,
        changeNav,
        subNav,
        changeSubNav,
        SUB_NAV,
        TAB_ID,
        initNav,
        isSend,
    };
});

export default useNav;
