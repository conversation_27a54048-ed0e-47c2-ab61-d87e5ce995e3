<!-- src/components/common/task-completion-banner.vue -->
<template>
    <div
        class="task-completion-banner"
    >
        <div
            class="bg-default relative h-[88.5px] w-[322.5px] flex items-center justify-between px-[16px]"
            :style="`background-image: url(${requireImg('<EMAIL>')})`">
            <!-- 左侧内容 -->
            <div class="flex flex-col justify-center">
                <div class="HY-font text-[14px] text-[#E4C59D] font-bold leading-[20px]">
                    {{ title }}
                </div>
                <div class="HY-font mt-[9px] text-[12px] text-[#E4C59D] leading-[16px] opacity-[0.7]">
                    {{ subtitle }}
                </div>
            </div>

            <!-- 右侧按钮 -->
            <div
                class="bg-default relative h-[66px] w-[65.5px] flex items-center"
                :style="`background-image: url(${requireImg('<EMAIL>')})`"
            >
                <div class="flex-center absolute right-[10px] top-[10px] h-[21px] w-[21px] flex rounded-[50%] bg-[#fd871f] text-[12px] text-[#fff]">X1</div>
                <div
                    class="bg-default ml-[-10px] mt-auto h-[23px] w-[87px] flex flex-shrink-0 items-center justify-center"
                    :style="`background-image: url(${requireImg(buttonBg)})`"
                    @click="handleClick">
                    <span class="HY-font text-[14px] text-[#080000] font-bold">
                        {{ buttonText }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, nextTick, onMounted, ref } from 'vue';

const props = defineProps({
    // 主标题
    title: {
        type: String,
        default: '下单并完成订单',
    },
    // 副标题
    subtitle: {
        type: String,
        default: '今日完成0/1次',
    },
    // 按钮文字
    buttonText: {
        type: String,
        default: '去完成',
    },
    isFinish: {
        type: Boolean,
        default: false,
    },

});

const emit = defineEmits(['click']);
// 计算按钮背景图片
const buttonBg = computed(() => {
    return props.isFinish ? '<EMAIL>' : '<EMAIL>';
});
// 处理点击事件
function handleClick() {
    emit('click');
}
</script>

<style lang="less" scoped>
.task-completion-banner {
}

// 响应式适配
@media (max-width: 375px) {
    .task-completion-banner {
        .bg-default {
            width: calc(100vw - 32px);
        }
    }
}
</style>
