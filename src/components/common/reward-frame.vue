<template>
    <div class="reward-frame-container">
        <div
            class="reward-frame"
            :class="frameClass"
            :style="frameStyle">
            <img
                class="reward-image"
                :src="rewardImageUrl"
                :alt="rewardName" />
        </div>

        <div
            v-if="showName"
            class="reward-name"
            :class="nameClass">
            <p
                class="reward-text"
                v-html="formattedName"></p>
        </div>

        <div
            v-if="tag"
            class="reward-tag">
            {{ tag }}
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    reward: {
        type: [Object, String],
        required: true,
    },
    size: {
        type: Number,
        default: 68.5,
    },
    frameType: {
        type: String,
        default: 'default', // default, special, extra-1, extra-2
        validator: value => ['default', 'special', 'extra-1', 'extra-2'].includes(value),
    },
    showName: {
        type: Boolean,
        default: true,
    },
    namePosition: {
        type: String,
        default: 'bottom', // bottom, overlay
        validator: value => ['bottom', 'overlay'].includes(value),
    },
    tag: {
        type: String,
        default: '',
    },
    num: {
        type: [Number, String],
        default: '',
    },
});

const rewardInfo = computed(() => {
    if (typeof props.reward === 'string') {
        return getRewardInfo(props.reward);
    }
    return getRewardInfo(props.reward?.resourceId || props.reward?.id);
});

const rewardImageUrl = computed(() => {
    return rewardInfo.value?.imageUrl || '';
});

const rewardName = computed(() => {
    return rewardInfo.value?.name || '';
});

// 格式化奖励名称
const formatRewardName = (reward, num = '') => {
    const rewardInfo = getRewardInfo(reward?.resourceId || reward?.id);
    const mark = rewardInfo?.mark;

    if (!mark || mark === '其他') {
        return `${rewardInfo?.name}${num ? `×${num}` : ''}`;
    }

    const price = mark !== '包裹' ? mark : `${rewardInfo.price}豆`;
    return `${rewardInfo?.name}${num ? `×${num}` : ''}<br/>${price}`;
};

const formattedName = computed(() => {
    if (typeof props.reward === 'string') {
        return formatRewardName({ id: props.reward }, props.num);
    }
    return formatRewardName(props.reward, props.num);
});

const frameClass = computed(() => ({
    'frame-default': props.frameType === 'default',
    'frame-special': props.frameType === 'special',
    'frame-extra-1': props.frameType === 'extra-1',
    'frame-extra-2': props.frameType === 'extra-2',
}));

const nameClass = computed(() => ({
    'name-bottom': props.namePosition === 'bottom',
    'name-overlay': props.namePosition === 'overlay',
}));

const frameStyle = computed(() => {
    // 对于 extra-1 和 extra-2 类型，使用固定尺寸
    if (props.frameType === 'extra-1' || props.frameType === 'extra-2') {
        return {};
    }
    // 对于其他类型，使用动态尺寸
    return {
        width: `${props.size}px`,
        height: `${props.size}px`,
    };
});
</script>

<style lang="less" scoped>
.reward-frame-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .reward-frame {
        position: relative;
        overflow: hidden;
        box-sizing: border-box;
        padding: 7px;
        display: flex;
        justify-content: center;
        align-items: center;

        &.frame-default {
            background-image: url('@/assets/img/<EMAIL>');
            background-size: 100% 100%;
        }

        &.frame-special {
            background-image: url('@/assets/img/<EMAIL>');
            background-size: 100% 100%;
        }

        &.frame-extra-1 {
            width: 52px;
            height: 52px;
            background: #dfd2ac;
            border: 2px solid #ffffff;
            border-radius: 5px;
        }

        &.frame-extra-2 {
            width: 52px;
            height: 52px;
            background: #a4e29d;
            border: 2px solid #ffffff;
            border-radius: 5px;
        }

        .reward-image {
            width: 90%;
            height: 90%;
            object-fit: contain;
        }
    }

    .reward-name {
        &.name-bottom {
            width: 64px;
            height: 30.5px;
            margin-top: 2px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 10px;
            font-weight: 400;
            text-align: center;
            color: #ff5f20;
            line-height: 12px;
            letter-spacing: 0.24px;
        }

        &.name-overlay {
            position: absolute;
            bottom: -24px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1;

            .reward-text {
                white-space: nowrap;
                font-size: 10px;
                color: #ff5f20;
            }
        }
    }

    .reward-tag {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #ff6b6b;
        color: white;
        font-size: 8px;
        padding: 2px 4px;
        border-radius: 3px;
        z-index: 10;
    }
}
</style>
