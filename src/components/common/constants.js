// 公共常量配置文件

// 子导航配置
export const SUB_NAV_CONFIG = [
    {
        value: 1,
        activeImg: requireImg('<EMAIL>'),
        inactiveImg: requireImg('<EMAIL>'),
    },
    {
        value: 2,
        activeImg: requireImg('<EMAIL>'),
        inactiveImg: requireImg('<EMAIL>'),
    },
    {
        value: 3,
        activeImg: requireImg('<EMAIL>'),
        inactiveImg: requireImg('<EMAIL>'),
    },
];

export const SUB_NAV_TAB2_CONFIG = [
    {
        value: 1,
        activeImg: requireImg('<EMAIL>'),
        inactiveImg: requireImg('<EMAIL>'),
        other: 2,
    },
    {
        value: 2,
        activeImg: requireImg('<EMAIL>'),
        inactiveImg: requireImg('<EMAIL>'),
        other: 1,
    },
];

// 排行榜图标映射
export const RANK_ICON_MAP = {
    1: requireImg('<EMAIL>'),
    2: requireImg('<EMAIL>'),
    3: requireImg('<EMAIL>'),
};

// 等级经验配置
export const LEVEL_EXP_MAP = {
    0: 20,
    1: 20,
    2: 20,
    3: 20,
    4: 20,
    5: 20,
    6: 20,
    7: 20,
    8: 20,
};

// 保底奖励配置
export const GUARANTEED_REWARD_MAP = {
    low: {
        0: 'Gift6',
        1: 'Gift6',
        2: 'Gift6',
        3: 'Gift6',
        4: 'Gift6',
        5: 'Gift6',
        6: 'Gift6',
        7: 'Gift6',
        8: 'Gift6',
        9: 'Gift6',
    },
    high: {
        0: 'Gift6',
        1: 'Gift6',
        2: 'Gift6',
        3: 'Gift6',
        4: 'Gift6',
        5: 'Gift6',
        6: 'Gift6',
        7: 'Gift6',
        8: 'Gift6',
        9: 'Gift6',
    },
};

// 样式常量
export const STYLE_CONSTANTS = {
    CONTAINER_WIDTH: 375,
    TAB_HEIGHT: 28,
    TAB_WIDTH: 113.5,
    AVATAR_SIZE: 47,
    REWARD_FRAME_SIZE: 68.5,
    USER_RANK_SIZE: 28,
};

// 排行榜类型
export const RANK_TYPE = {
    SEND: 1,
    RECEIVE: 2,
};

// 日期类型
export const DATE_TYPE = {
    TOTAL: 1,
    COMMON: 2,
};
