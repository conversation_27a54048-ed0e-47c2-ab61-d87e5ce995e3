<template>
    <div class="sub-nav-tabs">
        <div
            v-for="item of navConfig"
            :key="item.value"
            class="nav-tab"
            @click="handleNavChange(item.value)"
        >
            <img
                class="nav-tab-image"
                :src="props.modelValue === item.value ? item.activeImg : item.inactiveImg"
                :alt="item.label"
            />
        </div>
    </div>
</template>

<script setup>
import { SUB_NAV_CONFIG } from './constants';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 1,
    },
    navConfig: {
        type: Array,
        default: () => SUB_NAV_CONFIG,
    },
});

const emit = defineEmits(['update:modelValue', 'change']);

const handleNavChange = (value) => {
    if (props.modelValue === value)
        return;

    emit('change', value);
};
</script>

<style lang="less" scoped>
.sub-nav-tabs {
    display: flex;
    align-items: center;
    justify-content: center;

    .nav-tab {
        position: relative;
        margin-right: 12px;
        width: 104.5px;
        height: 43.5px;
        cursor: pointer;

        &:last-child {
            margin-right: 0;
        }

        .nav-tab-image {
            height: 100%;
            width: 100%;
            object-fit: contain;
        }
    }
}
</style>
