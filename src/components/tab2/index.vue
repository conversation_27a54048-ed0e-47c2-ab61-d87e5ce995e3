<template>
    <div class="my-bg relative mt-[24px] w-[100%] flex flex-col items-center">
        <div class="relative mx-auto h-[37px] flex items-center justify-center">
            <img
                class="h-[37px]"
                :src="SUB_NAV[SUB_NAV[subNav - 1].other - 1].inactiveImg"
                @click="handleSubNavChange">
            <img
                v-if="subNav === 1"
                class="absolute left-[0] h-[37px]"
                :src="SUB_NAV[0].activeImg"
            >
            <img
                v-if="subNav === 2"
                class="absolute right-[0] h-[37px]"
                :src="SUB_NAV[1].activeImg"
            >
        </div>
        <div
            v-if="subNav === 1"
            class="bg-default relative h-[542.5px] w-[375px] flex flex-shrink-0 flex-col flex-col items-center pt-[79px]"
            :style="`background-image: url(${requireImg('<EMAIL>')})`"
        >
            <task-completion-banner
                title="下单并完成订单"
                :subtitle="`今日完成${store.taskList[0]}/1次`"
                :is-finish="store.taskList[0] > 0"
                @click="jumpToLink(RULE_LINK)"
            />
            <task-completion-banner
                class="mt-[12px]"
                title="在娱乐房为完单大神送任意豆豆礼物"
                :subtitle="`今日完成${store.taskList[1]}/1次`"
                :is-finish="store.taskList[1] > 0"
                @click="goTask"
            />
            <div
                class="task-completion-banner"
            >
                <div
                    class="bg-default relative mt-[12px] h-[188px] h-[188px] w-[322.5px] w-[322.5px] flex flex-col items-center px-[16px]"
                    :style="`background-image: url(${requireImg('<EMAIL>')})`">
                    <div class="w-[100%] flex flex-row justify-between">
                        <!-- 左侧内容 -->
                        <div class="mt-[31px] flex flex-col">
                            <div class="HY-font text-[14px] text-[#E4C59D] font-bold leading-[20px]">
                                邀请一位新用户下单
                            </div>
                            <div class="HY-font mt-[9px] text-[12px] text-[#E4C59D] leading-[16px] opacity-[0.7]">
                                已完成{{ store.taskList[2] }}次
                            </div>
                        </div>

                        <!-- 右侧按钮 -->
                        <div
                            class="bg-default relative mt-[10px] h-[66px] w-[65.5px] flex items-center"
                            :style="`background-image: url(${requireImg('<EMAIL>')})`"
                        >
                            <div class="flex-center absolute right-[10px] top-[10px] h-[21px] w-[21px] flex rounded-[50%] bg-[#fd871f] text-[12px] text-[#fff]">X1</div>
                            <div
                                class="bg-default ml-[-10px] mt-auto h-[23px] w-[87px] flex flex-shrink-0 items-center justify-center"
                                :style="`background-image: url(${requireImg('<EMAIL>')})`"
                                @click="goTask">
                            </div>
                        </div>
                    </div>

                    <div class="mt-[14px] h-[1px] w-[292.5px] border-[0.5px] border-[#BFA381] border-dashed">
                    </div>

                    <div class="mt-[19px] h-[27.5px] w-[286.5px] text-left text-[12px] text-[#b89a9a] leading-[15.5px] opacity-[0.7]">
                        新用户定义:活动期间未下过订单且没绑定过邀请关系的用户，每个设备仅统计一次
                    </div>

                    <div
                        class="mt-[16px] h-[13px] w-[100px] text-center text-[12px] text-[#c0a381] font-[500] underline"
                        @click="showInviteRecordModal"
                    >
                        我的邀请记录
                    </div>
                </div>
            </div>
        </div>
        <div
            v-if="subNav === 2"
            ref="containerRef"
            class="bg-default relative h-[266px] w-[375px] flex flex-shrink-0 flex-col items-center"
            :style="`background-image: url(${requireImg('<EMAIL>')})`">
            <div
                class="bg-default absolute top-[214px] h-[387px] w-[375px] flex flex-shrink-0 flex-col items-center"
                :style="`background-image: url(${requireImg('<EMAIL>')})`"></div>
            <div
                class="bg-default absolute left-[38px] top-[248px] h-[73.5px] w-[298.5px] flex flex-shrink-0 flex-col items-center"
                :style="`background-image: url(${requireImg('<EMAIL>')})`">
                <div class="stork-outside-blue HY-font mt-[12px] text-[17px] text-[#fff]">
                    送礼每累计5000豆可品尝1次
                </div>
                <div
                    class="bg-default relative h-[23.5px] w-[260.5px] flex items-center pl-[1px]"
                    :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }">
                    <img
                        class="h-[22px]"
                        :style="{
                            backgroundImage: `url(${requireImg('<EMAIL>')})`,
                            backgroundSize: 'cover',
                            width: `${((store.userSendPrice % 5000) / 5000) * 100}%`,
                        }"
                        alt="" />
                    <div
                        class="absolute left-1/2 top-[5.5px] transform text-[11px] text-[#000000] -translate-x-1/2">
                        {{ store.userSendPrice % 5000 }}/5000
                    </div>
                </div>
            </div>
        </div>

        <div
            class="bg-default relative mt-[0px] h-[167.5px] w-[374px] flex px-[13px] pt-[60px]"
            :style="`background-image: url(${requireImg('<EMAIL>')})`">
            <div class="ml-[5px] flex flex-1 overflow-x-auto">
                <broadcast
                    direction="x"
                    :allow-touch="true"
                    :data="curRwd?.rwdList || []">
                    <template #default="{ item }">
                        <RewardFrame
                            :reward="item"
                            :size="68.5"
                            frame-type="special"
                            class="mr-[10px] flex-shrink-0" />
                    </template>
                </broadcast>
            </div>
        </div>
        <draw-modal></draw-modal>
        <invite-record-modal></invite-record-modal>
    </div>
</template>

<script setup>
import useDraw from './use-draw';
import InviteRecordModal from './invite-record-modal.vue';
import useInitStore from '@/stores/modules/use-init-store';
import { useSubNavigation } from '@/hooks/useSubNavigation';
import RewardFrame from '@/components/common/reward-frame.vue';
import { jumpRandomRoom } from '@/api';
import { SUB_NAV_TAB2_CONFIG as SUB_NAV } from '@/components/common/constants';
import TaskCompletionBanner from '@/components/common/task-completion-banner.vue';

const showInviteRecordModal = () => {
    useEventBus('invite-record-modal').emit({ show: true });
};

const initStore = useInitStore();

const store = useDraw();
const { subNav, changeSubNav } = useSubNavigation(1, SUB_NAV);

function handleRefresh() {
    if (subNav.value === 1) {
        store.getTaskInfo();
        return;
    }
    store.flashDrawInfo();
}

const goTask = async () => {
    if (store.taskList.value[1] > 0) {
        return;
    }
    const [{ code, data }] = await jumpRandomRoom({
    });
    if (code === 0) {
        console.log(data.cid);
        toRoom(data.cid);
    }
};

const handleSubNavChange = async () => {
    await changeSubNav(SUB_NAV[subNav.value - 1].other, handleRefresh);
};

const containerRef = ref();

onMounted(async () => {
    await store.init();
});
</script>

<style lang="less" scoped>
/*设置IOS页面长按不可复制粘贴，但是IOS上出现input、textarea不能输入，因此将使用-webkit-user-select:auto;*/
* {
    -webkit-touch-callout: none;
    /*系统默认菜单被禁用*/
    -webkit-user-select: none;
    /*webkit浏览器*/
    -khtml-user-select: none;
    /*早期浏览器*/
    -moz-user-select: none;
    /*火狐*/
    -ms-user-select: none;
    /*IE10*/
    user-select: none;
}

input,
textarea {
    -webkit-user-select: auto;
    /*webkit浏览器*/
    margin: 0px;
    padding: 0px;
    outline: none;
}

* {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-tap-highlight-color: transparent;
    /* For some Androids */
}

.btn2 {
    // pointer-events: none; //使元素成为鼠标事件的目标
}
.v-enter-active,
.v-leave-active {
    transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
    opacity: 0;
}
</style>
