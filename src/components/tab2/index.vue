<template>
    <div class="my-bg relative mt-[40px] w-[100%] flex flex-col items-center">
        <div
            ref="containerRef"
            class="bg-default relative h-[266px] w-[375px] flex flex-shrink-0 flex-col items-center"
            :style="`background-image: url(${isLow ? requireImg('<EMAIL>') : requireImg('<EMAIL>')})`">
            <div
                class="bg-default absolute top-[214px] h-[387px] w-[375px] flex flex-shrink-0 flex-col items-center"
                :style="`background-image: url(${isLow ? requireImg('<EMAIL>') : requireImg('<EMAIL>')})`"></div>
            <div
                class="bg-default absolute left-[38px] top-[248px] h-[73.5px] w-[298.5px] flex flex-shrink-0 flex-col items-center"
                :style="`background-image: url(${isLow ? requireImg('<EMAIL>') : requireImg('<EMAIL>')})`">
                <div class="stork-outside-blue HY-font mt-[12px] text-[17px] text-[#fff]">
                    送礼每累计5000豆可品尝1次
                </div>
                <div
                    class="bg-default relative h-[23.5px] w-[260.5px] flex items-center pl-[1px]"
                    :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }">
                    <img
                        class="h-[22px]"
                        :style="{
                            backgroundImage: `url(${requireImg('<EMAIL>')})`,
                            backgroundSize: 'cover',
                            width: `${((store.userSendPrice % 5000) / 5000) * 100}%`,
                        }"
                        alt="" />
                    <div
                        class="absolute left-1/2 top-[5.5px] transform text-[11px] text-[#000000] -translate-x-1/2">
                        {{ store.userSendPrice % 5000 }}/5000
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-[65px] w-full flex items-center justify-center text-[12px] text-[#F22866]">
            已品尝:{{ store.userDrawTimes }}次
        </div>
        <div
            class="bg-default relative mt-[0px] h-[167.5px] w-[374px] flex px-[13px] pt-[60px]"
            :style="`background-image: url(${isLow ? requireImg('<EMAIL>') : requireImg('<EMAIL>')})`">
            <div class="ml-[5px] flex flex-1 overflow-x-auto">
                <broadcast
                    direction="x"
                    :allow-touch="true"
                    :data="curRwd?.rwdList || []">
                    <template #default="{ item }">
                        <RewardFrame
                            :reward="item"
                            :size="68.5"
                            frame-type="special"
                            class="mr-[10px] flex-shrink-0" />
                    </template>
                </broadcast>
            </div>
        </div>
        <draw-modal :type="isLow ? 0 : 1"></draw-modal>
    </div>
</template>

<script setup>
import dayjs from 'dayjs';
import { ref } from 'vue';
import { highRwdMap, lowRwdMap } from './config';
import useDraw from './use-draw';
import useInitStore from '@/stores/modules/use-init-store';
import { useSubNavigation } from '@/hooks/useSubNavigation';
import RewardFrame from '@/components/common/reward-frame.vue';

const initStore = useInitStore();

const store = useDraw();
const { subNav, isLow, changeSubNav } = useSubNavigation(1);

const containerRef = ref();

onMounted(async () => {
    await store.init();
});
</script>

<style lang="less" scoped>
/*设置IOS页面长按不可复制粘贴，但是IOS上出现input、textarea不能输入，因此将使用-webkit-user-select:auto;*/
* {
    -webkit-touch-callout: none;
    /*系统默认菜单被禁用*/
    -webkit-user-select: none;
    /*webkit浏览器*/
    -khtml-user-select: none;
    /*早期浏览器*/
    -moz-user-select: none;
    /*火狐*/
    -ms-user-select: none;
    /*IE10*/
    user-select: none;
}

input,
textarea {
    -webkit-user-select: auto;
    /*webkit浏览器*/
    margin: 0px;
    padding: 0px;
    outline: none;
}

* {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-tap-highlight-color: transparent;
    /* For some Androids */
}

.btn2 {
    // pointer-events: none; //使元素成为鼠标事件的目标
}
.v-enter-active,
.v-leave-active {
    transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
    opacity: 0;
}
</style>
