<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="dialog-wrapper">
            <div class="w-[100%] flex px-[30px] text-[13px] text-[#E4C59D]">
                <div class="flex-1 text-center"> 奖励</div>
                <div class="flex-1 text-center"> 时间</div>
            </div>
            <div
                ref="wrapper"
                class="scroll-wrapper px-[30px] pt-[6px]"
                @scroll="handleScroll"
            >
                <div
                    v-for="(item, index) of recordStore.list"
                    :key="index"
                    class="under-line relative mb-[6px] w-[100%] flex text-[11px] text-[#E4C59D]"
                >
                    <div
                        class="flex-1 text-center"
                        v-html="dayjs.unix(item.time).tz().format('MM-DD <br/> HH:mm:ss')">
                    </div>
                    <div
                        class="flex-1 pr-[20px] text-left"
                        v-html="handleStr(item)">
                    </div>
                </div>
                <div
                    v-if="!recordStore.list?.length"
                    class="mt-[40px] w-[100%] text-center text-[14px] text-[#FF5F20]"
                >
                    暂无记录
                </div>
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { throttle } from 'lodash-es';
import useDrawRecords from './use-draw-records';

const props = defineProps({
    type: {
        type: Number,
        default: 0,
    },
});
const wrapper = ref();

const recordStore = useDrawRecords();

const handleStr = (data) => {
    const text = `获得:`;
    const rewardList = Object.keys(data.drawInfo).map(key => ({ id: key, num: data.drawInfo[key] }));
    const str = rewardList.map(item => `${getRewardInfo(item.id).name}*${item.num}`).join(',');

    return `${text}${str}`;
};

const handleScroll = () => {
    const scrollWrap = wrapper.value;
    if (scrollWrap.scrollTop + scrollWrap.clientHeight >= scrollWrap.scrollHeight - 30) {
        recordStore.nextPage();
    }
};

const isShow = ref(false);
useEventBus('draw-record-modal').on(({ show = true }) => {
    isShow.value = show;
    recordStore.reset();
    recordStore.nextPage();
});
</script>

<style lang="less" scoped>
.dialog-wrapper {
    position: relative;
    width: 279.5px;
    height: 315.5px;
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100% 100%;
    padding-top: 127px;
    box-sizing: border-box;

    .scroll-wrapper {
        width: 100%;
        height: 164px;
        margin: auto;
        box-sizing: border-box;
        overflow-y: auto;
        overflow-x: hidden;

        .record-item {
            width: 100%;
            font-size: 13px;
            font-weight: normal;
            text-align: left;
            color: #ff5f20;
            margin: auto;
            padding: 10px 0 14px 14px;
            display: flex;
            flex-direction: column;
        }
    }

    .btns {
        position: absolute;
        bottom: 25px;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: center;

        .btn {
            width: 181px;
            height: 44px;

            &:first-child {
                margin-right: 20px;
            }
        }
    }
}

.under-line {
    border-bottom: 0.25px solid #ff5f20;
}
</style>
