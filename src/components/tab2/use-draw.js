import dayjs from 'dayjs';
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import useInitStore from '../../stores/modules/use-init-store';
import useLoading from '@/hooks/useLoading';
import { draw as drawApi, getDrawBroadcast, getDrawInfo } from '@/api/index';

const useDrawStore = defineStore('draw', () => {
    const broadcastsList = ref([]);
    const initStore = useInitStore();

    const tickets = ref(0);

    const userDrawTimes = ref(0);

    const isLoading = ref(false);
    useLoading(isLoading);
    const rankList = ref([]);

    const self = ref({});

    const draw = async (req) => {
        try {
            const [{ code, data }] = await drawApi(req);
            return { code, data };
        }
        catch (e) {
            return false;
        }
    };

    const getLotteryBroadcast = async () => {
        try {
            const [{ code, data }] = await getDrawBroadcast();
            broadcastsList.value = data?.list || [];
            return { code, data };
        }
        catch (e) {
            return false;
        }
    };

    const flashDrawInfo = async () => {
        isLoading.value = true;
        try {
            const [{ code, data }] = await getDrawInfo();
            tickets.value = data?.tickets || 0;

            return { code, data };
        }
        finally {
            isLoading.value = false;
        }
    };

    const init = async () => {
        await flashDrawInfo();
    };

    return {
        tickets,
        broadcastsList,
        draw,
        flashDrawInfo,
        getLotteryBroadcast,
        init,
        isEnd: dayjs
            .unix(initStore.serverTime)
            .isAfter(dayjs.unix(initStore.initData.endTime).add(1, 'day')),
        userDrawTimes,
        rankList,
        self,
    };
});

export default useDrawStore;
