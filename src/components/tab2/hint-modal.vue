<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div
            class="bg-default h-[288px] w-[375px] flex flex-col items-center pt-[100px]"
            :style="`background-image: url(${type === 0 ? requireImg('<EMAIL>') : requireImg('<EMAIL>')})`"
        >
            <div class="h-[84px] w-[240px] text-center text-[12px] text-[#FF5F20] leading-[18px] tracking-[0.24px] font-[500]">
                每参与1次，可获得1经验
                <br />积攒经验可提升奖池等级并解锁更多奖励
                <div class="h-[4px]"></div>
                *等级越高，福利奖池内奖励越好 <br />
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
    type: {
        type: Number,
        default: 0,
    },
});

const isShow = ref(false);
useEventBus('hint-modal').on((params) => {
    isShow.value = params.show;
});
const closeDialog = () => {
    isShow.value = false;
};
</script>

<style lang="less" scoped>

</style>
