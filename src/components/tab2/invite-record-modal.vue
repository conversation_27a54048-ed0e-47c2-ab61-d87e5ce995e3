<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true">
        <img
            :src="requireImg('<EMAIL>')"
            class="absolute right-[25px] top-[-20px] z-30 h-[30px] w-[30px]"
            @click="closePopup" />
        <div
            class="bg-default h-[381.5px] w-[375px] flex flex-shrink-0 flex-col items-center pt-[78px]"
            :style="`background-image: url(${requireImg('<EMAIL>')})`">
            <div
                v-for="item in store.hotSpringTaskData"
                class="mb-[20px] w-[100%] flex flex-col">
                <div class="text-center text-[12px] text-[#FF5F20]">
                    活动期内送出以下礼物可获得奖励
                </div>
                <div
                    class="bg-default relative ml-[90px] mt-[8px] h-[115px] w-[247px] flex items-center pl-[29px]"
                    :style="`background-image: url(${requireImg('<EMAIL>')})`">
                    <div class="absolute left-[-54px] top-[-13px]">
                        <van-swipe
                            class="h-[128.5px] w-[77.5px]"
                            autoplay="3000"
                            :touchable="false"
                            :show-indicators="false">
                            <van-swipe-item
                                v-for="(rwd, index) of rewardMap[item.taskId]"
                                :key="index">
                                <div
                                    class="bg-default h-[128.5px] w-[77.5px] flex flex-col items-center"
                                    :style="`background-image: url(${requireImg('<EMAIL>')})`">
                                    <img
                                        class="mt-[23px] h-[54px] w-[54px] object-cover"
                                        :src="getRewardInfo(rwd.id).imageUrl"
                                        alt="" />
                                    <div class="name z-1 text-[#0068BA]">
                                        <p
                                            class="whitespace-nowrap"
                                            v-html="
                                                `${getRewardName({ id: rwd.id })}${rwd.days}天`
                                            "></p>
                                    </div>
                                </div>
                            </van-swipe-item>
                        </van-swipe>
                    </div>
                    <img
                        class="absolute left-[-73px] top-[-7px] h-[26px] w-[56.5px]"
                        :src="requireImg('<EMAIL>')" />
                    <div v-for="({ resourceId, num }, index) of item.giftInfoList">
                        <div class="reward-frame relative">
                            <img
                                class="reward-img"
                                :src="getRewardInfo(resourceId).imageUrl" />
                            <div class="absolute bottom-[0] right-[2px] text-[12px] text-[#FF5F20]">
                                {{ num }}/{{ 1 }}
                            </div>
                        </div>
                        <div class="name z-1 mt-[2px]">
                            <p
                                class="whitespace-nowrap"
                                v-html="getRewardName({ id: resourceId })"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
import useBookTaskStore from './use-book-task-store';
import { pageView } from '@/utils/jsbridge';

const isShow = ref(false);

const store = useBookTaskStore();
useEventBus('book-popup').on(({ show = true }) => {
    isShow.value = show;
    store.update();
    pageView('activity_illustration_page');
});
const closePopup = () => {
    isShow.value = false;
};

const getRewardName = (reward) => {
    const rewardInfo = getRewardInfo(reward?.id);
    const mark = rewardInfo?.mark;
    return `${rewardInfo?.name}<br/>${rewardInfo.price ? `${rewardInfo.price}豆` : `${rewardInfo.special_type || ''}${mark || ''}`}`;
};

const rewardMap = {
    1: [
        { id: 'HB_1', days: 7 },
        { id: 'HB_2', days: 7 },
        { id: 'HB_3', days: 7 },
    ],
    2: [
        { id: 'HB_4', days: 3 },
        { id: 'HB_5', days: 3 },
    ],
};
</script>

<style lang="less" scoped>
.reward-frame {
    position: relative;
    overflow: hidden;
    width: 64px;
    height: 64px;
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 7px;
    margin-right: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .reward-img {
        width: 48px;
        height: 48px;
        object-fit: contain;
    }
}
.name {
    width: 64px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 10px;
    font-weight: 400;
    text-align: center;
    color: #ff5f20;
    line-height: 14px;
    margin-top: 18px;
}
</style>
