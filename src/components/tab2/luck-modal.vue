<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div
            class="bg-default h-[338.5px] w-[356px] flex flex-col items-center pt-[108px]"
            :style="`background-image: url(${requireImg('<EMAIL>')})`"
        >
            <div class="flex">
                <div
                    v-for="(reward, index) of rwdList"
                    :key="`reward-${index}`"
                    class="reward">
                    <div
                        class="reward-frame relative"
                    >
                        <img
                            class="reward-img"
                            :src="getRewardInfo(reward?.id).imageUrl" />
                    </div>
                    <div class="name z-1 mt-[-14px]">
                        <p
                            class="whitespace-nowrap"
                            v-html="
                                getRewardName(reward, reward?.num)
                            "></p>
                    </div>

                    <div class="text-center text-[11px] text-[#f22866] tracking-[-0.22px] font-[500]">每日限{{ reward.num }}个</div>
                </div>
            </div>
            <div class="mt-[4px] h-[48px] w-[182px] text-center text-[12px] text-[#f22866] leading-[18px] tracking-[0.24px] font-[500]">
                每日12点-24点<br />品尝有机会额外获得幸运赠礼<br />奖励数量每日重置，先到先得
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
    type: {
        type: Number,
        default: 0,
    },
});

const rwdList = [
    { id: 'Add_Gift_1', num: 50 },
    { id: 'Add_Gift_2', num: 100 },
];

const getRewardName = (reward, num) => {
    const rewardInfo = getRewardInfo(reward?.id);
    const mark = rewardInfo?.mark;
    if (!mark || mark === '其他') {
        return `${rewardInfo?.name}`;
    }
    return `${rewardInfo?.name}<br/>${(mark !== '包裹' ? mark : `${rewardInfo.price}豆`)}`;
};
const isShow = ref(false);
useEventBus('luck-modal').on((params) => {
    isShow.value = params.show;
});
const closeDialog = () => {
    isShow.value = false;
};
</script>

<style lang="less" scoped>
    .reward {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 10px;

    .name {
        width: 62.5px;
        height: 30.5px;
        background: #fd7271;
        border: 1px solid #f22866;
        border-radius: 5px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 11px;
        font-weight: 400;
        text-align: center;
        color: #ffffff;
        line-height: 12px;
        letter-spacing: 0.24px;
    }
}

.reward-frame {
    position: relative;
    overflow: hidden;
    width: 74.5px;
    height: 74.5px;
    background-image: url('@/assets/img/<EMAIL>');
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 7px;
    margin-right: 5px;
    margin-left: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .reward-img {
        width: 64px;
        height: 64px;
        object-fit: contain;
    }
}
</style>
