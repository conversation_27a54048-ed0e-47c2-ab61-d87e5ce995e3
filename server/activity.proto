
syntax = "proto3";

import "common.proto";

package activity;

service Activity {
  // 初始
  rpc init(initReq) returns(initRes);
  // 榜单
  rpc getRank(getRankReq) returns (getRankRes);
  // 抽奖
  rpc draw(drawReq) returns (drawRes);
  // 抽奖播报
  rpc getDrawBroadcast(initReq) returns (getDrawBroadcast);
  // 我的获奖记录
  rpc myPrizeRecord(myPrizeRecordReq) returns(myPrizeRecordRes);
  // 获取抽奖信息
  rpc getDrawInfo(initReq) returns(getDrawInfoRes);
  // 获取任务列表
  rpc getTaskList(initReq) returns(getTaskListRes);
  // 我的邀请记录
  rpc myInviteRecord(myInviteRecordReq) returns(myInviteRecordRes);
  // 跳转随机房间
  rpc jumpRandomRoom(initReq) returns(jumpRandomRoomRes);
  // 接受邀请 接口
  rpc invite(inviteReq) returns(emptyRes);
}

message initReq {
  uint32 uid = 1; // 当前用户uid
  string token = 2; // 当前用户token
}

message initRes {
  uint32 serverTime = 1; // 服务器时间
  uint32 startTime = 2; // 活动开始时间
  uint32 endTime = 3;  // 活动结束时间
  common.UserInfo userInfo = 4; // 用户信息
  bool isDs = 5; // 是否是大神
  uint32 orderUserNum = 6; // 完单人数
}

message getTaskListRes {
  repeated uint32 list = 1; // 任务列表 [1,2,3] 每日完成任务次数，按文档顺序排序
}

message drawReq {
  uint32 uid = 1;
  string token = 2; // 当前用户token
  uint32 drawCount = 3 [default=1]; // 抽奖次数：1(默认值)
}

message drawRes {
  map<string, uint32> drawInfo = 1; // {A1: 5, A2: 3} resourceId: num
}

message myPrizeRecordReq {
  uint32 page          = 1 [default=1];
  uint32 size          = 2 [default=10];
  uint32 uid           = 3; // 当前操作用户uid
}

message myPrizeRecordRes {
  message recordInfo {
    uint32 time = 1; // 获得时间
    uint32 tickets = 2; // 消耗的抽奖券数量
    map<string, uint32> drawInfo = 3; // 获得的奖励 {A1: 5, A2: 3} resourceId: num
  }
  repeated recordInfo list = 1;
  uint32 total = 2;
}

message getDrawInfoRes {
  uint32 tickets = 1; // 可用数量
}

message getDrawBroadcast {
  message broadcastItem {
    common.UserInfo userInfo = 1; // 用户信息
    string resourceId = 2; // 奖励资源id
  }
  repeated broadcastItem list = 1; // 播报列表
  uint32 total = 2; // 总数
}

message getRankReq {
  uint32 page          = 1 [default=1];
  uint32 size          = 2 [default=10];
  uint32 uid           = 3; // 当前操作用户uid
  optional string date = 4; // 榜单日期 YYYYMMDD 20230501 不传默认为总榜
  uint32 isDs = 5; // 是否是大神
}

message getRankRes {
  message Item {
      string rank = 1; // 榜单排名
      uint32 value = 2; // 榜单值
      common.UserInfo userInfo = 3; // 用户信息
      optional common.ChannelInfo channelInfo = 4; // 房间信息
      uint32 money = 5; // 现金
  }
  message myRankInfo {
    common.UserInfo userInfo = 1; // 用户信息
    string rank = 2; // 榜单排名
    uint32 value  = 3; // 榜单值
    string prevDescribe = 4; // 距上一名描述
    string nextDescribe = 5; // 超下一名描述
    uint32 money = 6; // 现金
  }
  uint32 total       = 1; // 总数
  repeated Item list = 2; // 列表信息
  myRankInfo self    = 3; // 底部我的排名信息
}

message myInviteRecordReq {
  uint32 page          = 1 [default=1];
  uint32 size          = 2 [default=10];
  uint32 uid           = 3; // 当前操作用户uid
}

message myInviteRecordRes {
  message Item {
    common.UserInfo userInfo = 1; // 用户信息
    uint32 status = 2; // 状态 1: 已受邀 2: 已下单
    uint32 acceptTime = 3; // 接受邀请时间
  }
  repeated Item list = 1;
  uint32 total = 2;
}

message jumpRandomRoomRes {
  uint32 cId = 1; // 随机房间id
}

message inviteReq {
  uint32 inviterUid = 1; // 邀请人uid
  uint32 uid  = 2; // 接受邀请人uid
  string token = 3; // 当前用户token
}
