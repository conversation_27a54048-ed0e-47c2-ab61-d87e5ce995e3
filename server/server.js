// /Users/<USER>/Documents/2025/sea-of-treasures-2025/server/server.js
import { fileURLToPath } from 'node:url';
import { dirname, join } from 'node:path';
import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import dayjs from 'dayjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// --- Helper Functions for Mock Data Generation ---
function generateUserInfo(uid, usernamePrefix = 'User') {
    return {
        uid,
        username: `tt110200509`,
        alias: `Alias_${uid}`,
        nickname: `Nickname ${uid}`,
        sex: uid % 2 === 0 ? 1 : 2, // 1 for male, 2 for female
        guildInfo: {
            name: `Guild_${String.fromCharCode(65 + (uid % 5))}`,
            guildId: 100 + (uid % 10),
            displayId: 1000 + (uid % 100),
        },
        role: uid % 3, // 0, 1, 2
    };
}

function generateChannelInfo(channelId) {
    return {
        channelId,
        status: channelId % 5, // Example status
        isPartChannel: channelId % 2 === 0,
    };
}

// 验证请求参数
function validateRequest(reqBody, requiredFields = []) {
    const errors = [];

    if (!reqBody) {
        errors.push('Request body is required');
        return errors;
    }

    requiredFields.forEach((field) => {
        if (reqBody[field] === undefined || reqBody[field] === null) {
            errors.push(`Field '${field}' is required`);
        }
    });

    return errors;
}

// --- Mock Data Definitions ---
const mockResponses = {
    // 初始化接口 - 符合 initRes 定义
    initRes: reqBody => ({
        serverTime: Math.floor(Date.now() / 1000),
        startTime: Math.floor(dayjs().subtract(7, 'day').valueOf() / 1000),
        endTime: Math.floor(dayjs().add(7, 'day').valueOf() / 1000),
        userInfo: generateUserInfo(reqBody.uid || 123456),
        isDs: (reqBody.uid || 123456) % 3 === 0, // 模拟是否是大神
        orderUserNum: Math.floor(Math.random() * 100) + 50, // 完单人数
    }),

    // 获取任务列表 - 符合 getTaskListRes 定义
    getTaskListRes: () => ({
        list: [3, 5, 2, 8, 1, 4, 6], // 每日完成任务次数，按文档顺序排序
    }),

    // 获取抽奖信息 - 符合 getDrawInfoRes 定义
    getDrawInfoRes: reqBody => ({
        tickets: Math.floor(Math.random() * 20) + 5, // 可用抽奖券数量
    }),

    // 我的获奖记录 - 符合 myPrizeRecordRes 定义
    myPrizeRecordRes: (reqBody) => {
        const { page = 1, size = 10 } = reqBody;
        const totalItems = 100;
        const list = [];

        for (let i = 0; i < Math.min(size, 10); i++) {
            const itemIndex = (page - 1) * size + i;
            if (itemIndex >= totalItems)
                break;

            list.push({
                time: Math.floor(Date.now() / 1000) - itemIndex * 3600,
                tickets: (i % 3) + 1, // 消耗的抽奖券数量
                drawInfo: {
                    [`A${(i % 5) + 1}`]: (i % 5) + 1,
                    [`A${(i % 3) + 6}`]: (i % 3) + 1,
                }, // 获得的奖励 {A1: 5, A2: 3} resourceId: num
            });
        }

        return {
            list,
            total: totalItems,
        };
    },

    // 抽奖播报 - 符合 getDrawBroadcast 定义
    getDrawBroadcast: () => ({
        list: [
            {
                userInfo: generateUserInfo(12345),
                resourceId: 'A1', // 奖励资源id
            },
            {
                userInfo: generateUserInfo(67890),
                resourceId: 'A2',
            },
            {
                userInfo: generateUserInfo(11111),
                resourceId: 'A3',
            },
        ],
        total: 3,
    }),

    // 榜单接口 - 符合 getRankRes 定义
    getRankRes: (reqBody) => {
        const { page = 1, size = 10, uid, date, isDs } = reqBody;
        const totalItems = 50;
        const list = [];

        for (let i = 0; i < Math.max(size, 10); i++) {
            const rank = (page - 1) * size + i + 1;
            if (rank > totalItems)
                break;

            const userUid = (uid || 123456) + i + 100;
            list.push({
                rank: rank.toString(),
                value: 100000 - rank * 1000, // 榜单值
                userInfo: generateUserInfo(userUid),
                channelInfo: generateChannelInfo(userUid + 1000),
                money: Math.floor(Math.random() * 10000) + 1000, // 现金
            });
        }

        return {
            total: totalItems,
            list,
            self: uid
                ? {
                        userInfo: generateUserInfo(uid),
                        rank: '5',
                        value: 95000,
                        prevDescribe: '距上一名还差500',
                        nextDescribe: '领先下一名1000',
                        money: Math.floor(Math.random() * 5000) + 2000,
                    }
                : null,
        };
    },

    // 我的邀请记录 - 符合 myInviteRecordRes 定义
    myInviteRecordRes: (reqBody) => {
        const { page = 1, size = 10 } = reqBody;
        const totalItems = 30;
        const list = [];

        for (let i = 0; i < Math.min(size, 10); i++) {
            const itemIndex = (page - 1) * size + i;
            if (itemIndex >= totalItems)
                break;

            list.push({
                userInfo: generateUserInfo(123456 + i + 300),
                status: (i % 2) + 1, // 状态 1: 已受邀 2: 已下单
                acceptTime: Math.floor(Date.now() / 1000) - itemIndex * 3600,
            });
        }

        return {
            list,
            total: totalItems,
        };
    },

    // 跳转随机房间 - 符合 jumpRandomRoomRes 定义
    jumpRandomRoomRes: () => ({
        cId: Math.floor(Math.random() * 10000) + 1000, // 随机房间id
    }),

    // 空响应 - 符合 emptyRes 定义
    emptyRes: () => ({}),

    // 抽奖接口 - 符合 drawRes 定义
    drawRes: (reqBody) => {
        const { drawCount = 1 } = reqBody;
        const drawInfo = {};

        // 根据抽奖次数生成奖励
        for (let i = 0; i < drawCount; i++) {
            const rewardId = `A${Math.floor(Math.random() * 10) + 1}`;
            drawInfo[rewardId] = (drawInfo[rewardId] || 0) + Math.floor(Math.random() * 5) + 1;
        }

        return { drawInfo };
    },

};

// --- API Route Definitions ---
// 严格按照 activity.proto 中的 Activity 服务定义
const apiEndpoints = {
    // 初始化接口
    init: { method: 'post', reqType: 'initReq', respType: 'initRes' },
    // 榜单接口
    getRank: { method: 'post', reqType: 'getRankReq', respType: 'getRankRes' },
    // 抽奖接口
    draw: { method: 'post', reqType: 'drawReq', respType: 'drawRes' },
    // 抽奖播报接口
    getDrawBroadcast: { method: 'post', reqType: 'initReq', respType: 'getDrawBroadcast' },
    // 我的获奖记录接口
    myPrizeRecord: { method: 'post', reqType: 'myPrizeRecordReq', respType: 'myPrizeRecordRes' },
    // 获取抽奖信息接口
    getDrawInfo: { method: 'post', reqType: 'initReq', respType: 'getDrawInfoRes' },
    // 获取任务列表接口
    getTaskList: { method: 'post', reqType: 'initReq', respType: 'getTaskListRes' },
    // 我的邀请记录接口
    myInviteRecord: { method: 'post', reqType: 'myInviteRecordReq', respType: 'myInviteRecordRes' },
    // 跳转随机房间接口
    jumpRandomRoom: { method: 'post', reqType: 'initReq', respType: 'jumpRandomRoomRes' },
    // 接受邀请接口
    invite: { method: 'post', reqType: 'inviteReq', respType: 'emptyRes' },
};

// 请求参数验证规则
const validationRules = {
    initReq: ['uid'],
    drawReq: ['uid'],
    getRankReq: ['uid'],
    myPrizeRecordReq: ['uid'],
    myInviteRecordReq: ['uid'],
    inviteReq: ['inviterUid', 'uid'],
};

Object.entries(apiEndpoints).forEach(([rpcName, config]) => {
    const routePath = `/activity.Activity/${rpcName}`;
    const responseGenerator = mockResponses[config.respType];

    if (!responseGenerator) {
        console.error(`No response generator found for ${config.respType} (RPC: ${rpcName})`);
        return;
    }

    if (config.method === 'get') {
        app.get(routePath, (req, res) => {
            try {
                if (process.env.NODE_ENV === 'development') {
                    console.log(`GET ${routePath}`);
                }

                const mockResponseData = responseGenerator(req.query);
                res.json({
                    code: 0,
                    msg: 'success',
                    data: mockResponseData,
                });
            }
            catch (error) {
                console.error(`Error in GET ${routePath}:`, error);
                res.status(500).json({
                    code: -1,
                    msg: 'Internal server error',
                    data: null,
                });
            }
        });
    }
    else if (config.method === 'post') {
        app.post(routePath, (req, res) => {
            try {
                if (process.env.NODE_ENV === 'development') {
                    console.log(`POST ${routePath} with body:`, req.body);
                }

                // 验证请求参数
                const requiredFields = validationRules[config.reqType] || [];
                const validationErrors = validateRequest(req.body, requiredFields);

                if (validationErrors.length > 0) {
                    return res.status(400).json({
                        code: 400,
                        msg: `Validation failed: ${validationErrors.join(', ')}`,
                        data: null,
                    });
                }

                const mockResponseData = responseGenerator(req.body);
                res.json({
                    code: 0,
                    msg: 'success',
                    data: mockResponseData,
                });
            }
            catch (error) {
                console.error(`Error in POST ${routePath}:`, error);
                res.status(500).json({
                    code: -1,
                    msg: 'Internal server error',
                    data: null,
                });
            }
        });
    }
});

// Serve static files (if any)
app.use(express.static(join(__dirname, 'public')));

// Start server
app.listen(PORT, () => {
    console.log(`Mock server is running on http://localhost:${PORT}`);
    console.log('Available RPC endpoints:');
    Object.entries(apiEndpoints).forEach(([rpcName, config]) => {
        console.log(`- ${config.method.toUpperCase()} /activity.Activity/${rpcName}`);
    });
});

export default app;
