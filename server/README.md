# Sea of Treasures 2025 - Mock Server

This is a mock server for the Sea of Treasures 2025 frontend application, providing mock data for API endpoints.

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm (comes with Node.js)

### Installation

1. Navigate to the server directory:
   ```bash
   cd server
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Server

Start the server in development mode (with auto-reload):

```bash
npm run dev
```

Or start in production mode:

```bash
npm start
```

The server will start on `http://localhost:3001` by default.

## Available Endpoints

- `POST /api/getServerTime` - Get server timestamp
- `POST /api/init` - Initialize application data
- `POST /api/getRankingData` - Get ranking data with pagination
- `POST /api/getSemiJuesaiGroupRankInfo` - Get semi-final group ranking info
- `POST /api/getSemiJuesaiUserGroupInfo` - Get user's group info for semi-finals
- `POST /api/getGuardCardInfo` - Get guard card info
- `POST /api/getPkRanking` - Get PK ranking
- `POST /api/initShop` - Initialize shop data
- `POST /api/getShopRecordUser` - Get user's shop records
- `POST /api/drawShop` - Draw from shop
- `POST /api/redeemBuffCardInShop` - Redeem buff card in shop
- `POST /api/initTreasure` - Initialize treasure data
- `POST /api/getTreasureRecordUser` - Get user's treasure records
- `POST /api/getTreasureBuffCard` - Get treasure buff card info

## Mock Data Structure

Mock data is defined in `server.js`. You can modify the `mockData` object to customize the responses.

## Configuration

- Port: The server runs on port 3001 by default. You can change this by setting the `PORT` environment variable.
- CORS: The server has CORS enabled to allow requests from any origin in development.

## Development

- The server uses `nodemon` for development with auto-reload.
- API requests are logged to the console using `morgan`.

## License

This project is licensed under the MIT License.
