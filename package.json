{"name": "live-anniversary-esports-venue", "type": "module", "version": "1.0.0", "private": true, "description": "Vue3.4 + Vite5 Template For MFv4", "scripts": {"dev": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite", "build": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build", "build-test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --sourcemap", "preview": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite preview", "pb": "node pb.cjs", "pb-p": "node pb-p.cjs", "lint": "eslint . --fix", "lint-staged": "lint-staged", "offline": "node build/manage-offline/index.cjs", "prepare": "husky"}, "dependencies": {"@galacean/effects": "^2.4.7", "@tt/active-app": "1.4.6", "@tt/bylink-sdk": "2.0.25", "@tt/open-install": "1.0.11", "@tt/tween": "1.0.1", "@vueuse/components": "^11.1.0", "@vueuse/core": "^10.11.1", "axios": "1.6.8", "dayjs": "^1.11.13", "dayjs-tz-format": "^1.0.1", "graphemer": "^1.4.0", "html2canvas": "^1.4.1", "lottie-web": "^5.12.2", "mockjs": "^1.1.0", "normalize.css": "^8.0.1", "pinia": "^2.2.4", "pinia-plugin-persistedstate": "^3.2.3", "swiper": "^11.1.14", "vant": "^4.9.7", "vue": "^3.5.11", "vue-router": "^4.4.5"}, "devDependencies": {"@antfu/eslint-config": "^2.27.3", "@eslint/eslintrc": "^3.1.0", "@tt/node-pngcrush": "^1.0.2", "@types/node": "^20.16.11", "@unocss/eslint-plugin": "^0.63.4", "@unocss/preset-rem-to-px": "^0.63.4", "@unocss/transformer-variant-group": "^0.63.4", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.20", "consola": "^3.2.3", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-plugin-format": "^0.1.2", "husky": "^9.1.6", "less": "^4.2.0", "lint-staged": "^15.2.10", "lodash-es": "^4.17.21", "postcss-mobile-forever": "4.1.6", "prettier": "^3.3.3", "protobufjs": "6.10.2", "rollup-plugin-visualizer": "^5.12.0", "sharp": "0.32.6", "svgo": "^3.3.2", "terser": "^5.34.1", "unocss": "^0.63.4", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "unplugin-vue-router": "^0.10.8", "vite": "^5.4.8", "vite-plugin-checker": "^0.6.4", "vite-plugin-image-optimizer": "^1.1.8", "vite-plugin-inspect": "^0.8.7", "vite-plugin-vue-devtools": "^7.4.6", "vite-plugin-vue-setup-extend": "^0.4.0"}, "lint-staged": {"*": "eslint --cache --fix"}}