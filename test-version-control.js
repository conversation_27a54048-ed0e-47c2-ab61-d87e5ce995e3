// 测试版本控制逻辑
import { createUseRankList } from './src/components/tab1/rank-list/use-rank-list.js';

// 模拟API函数
const mockApiFunction = async (params) => {
    return [{
        code: 0,
        msg: 'success',
        data: {
            total: 50,
            list: [
                {
                    rank: '1',
                    value: 99000,
                    userInfo: { uid: 123456, nickname: 'Test User 1' },
                    channelInfo: { channelId: 124556 },
                    money: 9945
                }
            ],
            self: {
                userInfo: { uid: 123456, nickname: 'Current User' },
                rank: '5',
                value: 95000,
                money: 3863
            }
        }
    }];
};

// 创建排行榜store
const useRankList = createUseRankList('test-rank', mockApiFunction);

// 测试版本控制
async function testVersionControl() {
    const store = useRankList();
    
    console.log('初始版本:', store.dataVersion.value);
    
    // 第一次加载数据
    await store.loadData();
    console.log('第一次加载后版本:', store.dataVersion.value);
    console.log('数据长度:', store.list.value.length);
    
    // 模拟快速连续请求（版本控制应该防止旧数据覆盖新数据）
    const promise1 = store.loadData();
    const promise2 = store.loadData();
    
    await Promise.all([promise1, promise2]);
    
    console.log('连续请求后版本:', store.dataVersion.value);
    console.log('最终数据长度:', store.list.value.length);
}

// 运行测试
testVersionControl().catch(console.error);
