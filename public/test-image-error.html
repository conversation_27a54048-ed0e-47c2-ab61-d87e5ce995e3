<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片错误处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-item h3 {
            margin-top: 0;
            color: #333;
        }
        .test-item img {
            max-width: 200px;
            height: auto;
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .hidden-img {
            display: none;
        }
        .error-info {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 5px;
        }
        .success-info {
            color: #27ae60;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>图片错误处理测试</h1>
        <p>这个页面用于测试图片加载失败时的错误处理机制。</p>
        
        <div class="test-item">
            <h3>测试1: 存在的图片</h3>
            <img src="/images/activity-header-bg.jpg" alt="活动背景" onload="showSuccess(this)" onerror="handleImageError(this)">
            <div class="info"></div>
        </div>
        
        <div class="test-item">
            <h3>测试2: 不存在的图片（应该显示占位图片）</h3>
            <img src="/images/nav-god-rank.png" alt="大神榜单" onload="showSuccess(this)" onerror="handleImageError(this)">
            <div class="info"></div>
        </div>
        
        <div class="test-item">
            <h3>测试3: 完全不存在的图片（应该隐藏）</h3>
            <img src="/images/non-existent-image.png" alt="不存在的图片" onload="showSuccess(this)" onerror="handleImageError(this)">
            <div class="info"></div>
        </div>
        
        <div class="test-item">
            <h3>测试4: 存在的图片</h3>
            <img src="/images/nav-god-task.png" alt="大神任务" onload="showSuccess(this)" onerror="handleImageError(this)">
            <div class="info"></div>
        </div>
    </div>

    <script>
        function handleImageError(img) {
            const infoDiv = img.parentElement.querySelector('.info');
            
            // 防止无限循环加载错误
            if (img.dataset.errorHandled) {
                // 如果已经处理过错误，隐藏图片元素
                img.style.display = 'none';
                infoDiv.innerHTML = '<div class="error-info">图片加载失败，已隐藏</div>';
                return;
            }
            
            // 标记已处理错误
            img.dataset.errorHandled = 'true';
            
            // 设置备用图片
            img.src = '/images/placeholder.png';
            infoDiv.innerHTML = '<div class="error-info">原图片加载失败，已显示占位图片</div>';
        }
        
        function showSuccess(img) {
            const infoDiv = img.parentElement.querySelector('.info');
            infoDiv.innerHTML = '<div class="success-info">图片加载成功</div>';
        }
    </script>
</body>
</html>
